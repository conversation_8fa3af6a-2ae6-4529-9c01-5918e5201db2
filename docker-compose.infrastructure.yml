services:
  # Redis for Event Streams
  redis:
    image: redis:7-alpine
    container_name: smoops-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    environment:
      - REDIS_REPLICATION_MODE=master
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - smoops-network

  # QuestDB for Time-Series Data
  questdb:
    image: questdb/questdb:7.3.10
    container_name: smoops-questdb
    ports:
      - "9000:9000"   # HTTP/REST API
      - "8812:8812"   # PostgreSQL wire protocol
      - "9009:9009"   # InfluxDB line protocol (ILP)
    volumes:
      - questdb_data:/var/lib/questdb
    environment:
      - QDB_CAIRO_COMMIT_LAG=1000
      - QDB_CAIRO_MAX_UNCOMMITTED_ROWS=10000
      - QDB_SHARED_WORKER_COUNT=2
      - QDB_HTTP_ENABLED=true
      - QDB_HTTP_BIND_TO=0.0.0.0:9000
      - QDB_PG_ENABLED=true
      - QDB_PG_BIND_TO=0.0.0.0:8812
      - QDB_LINE_TCP_ENABLED=true
      - QDB_LINE_TCP_BIND_TO=0.0.0.0:9009
      - QDB_TELEMETRY_ENABLED=false
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/status"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - smoops-network

  # PostgreSQL for Application Data
  postgres:
    image: postgres:15-alpine
    container_name: smoops-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=smartmarketoops
      - POSTGRES_USER=smoops_user
      - POSTGRES_PASSWORD=smoops_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U smoops_user -d smartmarketoops"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - smoops-network

  # Redis Commander for Redis Management (Optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: smoops-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - smoops-network
    profiles:
      - tools

  # QuestDB Console is built-in at port 9000
  # Access at http://localhost:9000

volumes:
  redis_data:
    driver: local
  questdb_data:
    driver: local
  postgres_data:
    driver: local

networks:
  smoops-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
