# Server Configuration
NODE_ENV=development
PORT=3001
HOST=0.0.0.0

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/smartmarket

# Authentication
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=your-refresh-jwt-secret-key-here
COOKIE_SECRET=your-cookie-secret-key-here
COOKIE_DOMAIN=localhost
ENCRYPTION_MASTER_KEY=your-32-char-encryption-key-here

# Session Configuration
SESSION_MAX_AGE=3600000                # 1 hour in milliseconds
REMEMBER_ME_MAX_AGE=2592000000         # 30 days in milliseconds
SESSION_INACTIVITY_TIMEOUT=1800000     # 30 minutes in milliseconds

# Client URLs
CLIENT_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000

# Email Configuration
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_USER=your-email-user
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=SmartMarket OOPS

# Delta Exchange API Configuration
DELTA_EXCHANGE_API_KEY=HmerKHhySssgFIAfEIh4CYA5E3VmKg
DELTA_EXCHANGE_API_SECRET=1YNVg1x9cIjz1g3BPOQPUJQr6LhEm8w7cTaXi8ebJYPUpx5BKCQysMoLd6FT
DELTA_EXCHANGE_TESTNET=true
DELTA_API_RATE_LIMIT=30
DELTA_API_RATE_WINDOW=60000
DELTA_EXCHANGE_WS_ENABLED=true
DELTA_EXCHANGE_WS_RECONNECT_INTERVAL=5000

# OpenAI API Key (for AI features)
OPENAI_API_KEY=your-openai-api-key

# Service Ports
ML_PORT=3002
FRONTEND_PORT=3000

# API Keys
OPENROUTER_API_KEY=your_openrouter_api_key

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001

# ML Service Configuration
PYTHONUNBUFFERED=1
TORCH_MPS_ENABLE=1 # For Apple Silicon GPU acceleration

# ML Bridge API Configuration
ML_API_URL=http://localhost:5000/api
ML_API_KEY=your-ml-api-key-here
ML_SYSTEM_RECONNECT_INTERVAL=30000 # 30 seconds
ML_HEALTH_CHECK_INTERVAL=300000 # 5 minutes
ML_BATCH_SIZE=20
ML_MAX_CONCURRENT_REQUESTS=5
ML_REQUEST_TIMEOUT=60000 # 60 seconds
ML_MAX_RETRIES=3
ML_AUTO_SIGNAL_GENERATION=false # Enable/disable automatic signal generation from ML predictions