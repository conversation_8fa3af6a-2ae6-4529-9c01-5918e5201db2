# ============================================================================
# SmartMarketOOPS - Unified Environment Configuration
# ============================================================================
# This is the consolidated environment configuration file.
# Copy this to .env and update with your actual values.
#
# SECURITY NOTE: Never commit .env files with real credentials to version control!
# ============================================================================

# ============================================================================
# SYSTEM CONFIGURATION
# ============================================================================

# Environment & Deployment
NODE_ENV=development                    # development | production | test
TRADING_MODE=test                       # test | live (CRITICAL: affects real money)
FORCE_TESTNET=true                      # Force testnet mode for safety

# Server Configuration
HOST=0.0.0.0                           # Server host
PORT=3001                               # Backend API port
FRONTEND_PORT=3000                      # Frontend development port
ML_PORT=3002                            # ML service port

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# PostgreSQL Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/smartmarket
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=smartmarket
POSTGRES_PORT=5432

# Redis Cache
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=                         # Leave empty for local development

# QuestDB (Time-series database)
QUESTDB_HOST=localhost
QUESTDB_PORT=9000
QUESTDB_HTTP_PORT=9009

# ============================================================================
# SECURITY & AUTHENTICATION
# ============================================================================

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here-change-this-in-production
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=your-refresh-jwt-secret-key-here-change-this
COOKIE_SECRET=your-cookie-secret-key-here-change-this
COOKIE_DOMAIN=localhost

# Encryption
ENCRYPTION_MASTER_KEY=your-32-char-encryption-key-here-change-this-in-production
ENCRYPTION_KEY_SECONDARY=your-secondary-encryption-key-here

# Session Configuration
SESSION_MAX_AGE=3600000                 # 1 hour in milliseconds
REMEMBER_ME_MAX_AGE=2592000000          # 30 days in milliseconds
SESSION_INACTIVITY_TIMEOUT=1800000      # 30 minutes in milliseconds

# ============================================================================
# CORS & CLIENT CONFIGURATION
# ============================================================================

# Client URLs
CLIENT_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001

# ============================================================================
# DELTA EXCHANGE API CONFIGURATION
# ============================================================================
# CRITICAL: These are EXAMPLE keys - replace with your actual Delta Exchange credentials
# Get your API keys from: https://www.delta.exchange/app/account/api

# Delta Exchange Credentials (REPLACE WITH YOUR ACTUAL KEYS)
DELTA_EXCHANGE_API_KEY=your-delta-exchange-api-key-here
DELTA_EXCHANGE_API_SECRET=your-delta-exchange-api-secret-here
DELTA_EXCHANGE_TESTNET=true             # ALWAYS start with testnet=true

# Delta Exchange Configuration
DELTA_API_RATE_LIMIT=30                 # Requests per minute
DELTA_API_RATE_WINDOW=60000             # Rate limit window in ms
DELTA_EXCHANGE_WS_ENABLED=true          # Enable WebSocket connections
DELTA_EXCHANGE_WS_RECONNECT_INTERVAL=5000 # WebSocket reconnect interval

# Product IDs (Environment-specific)
# Testnet: BTCUSD=84, ETHUSD=1699
# Production: BTCUSD=27, ETHUSD=3136
DELTA_BTCUSD_PRODUCT_ID=84              # Use 84 for testnet, 27 for production
DELTA_ETHUSD_PRODUCT_ID=1699            # Use 1699 for testnet, 3136 for production

# ============================================================================
# AI & ML CONFIGURATION
# ============================================================================

# OpenAI API (for AI features)
OPENAI_API_KEY=your-openai-api-key-here

# OpenRouter API (alternative AI provider)
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Anthropic API (for Claude models)
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# ML Service Configuration
PYTHONUNBUFFERED=1                      # Python output buffering
TORCH_MPS_ENABLE=1                      # Apple Silicon GPU acceleration
LOG_LEVEL=INFO                          # DEBUG | INFO | WARNING | ERROR

# ML Bridge API Configuration
ML_API_URL=http://localhost:3002/api
ML_API_KEY=your-ml-api-key-here
ML_SYSTEM_RECONNECT_INTERVAL=30000      # 30 seconds
ML_HEALTH_CHECK_INTERVAL=300000         # 5 minutes
ML_BATCH_SIZE=20
ML_MAX_CONCURRENT_REQUESTS=5
ML_REQUEST_TIMEOUT=60000                # 60 seconds
ML_MAX_RETRIES=3
ML_AUTO_SIGNAL_GENERATION=false         # Enable/disable automatic signal generation

# ============================================================================
# EMAIL CONFIGURATION (Optional)
# ============================================================================

# Email Service (for notifications)
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_USER=your-email-user
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=SmartMarket OOPS

# ============================================================================
# PRISMA CONFIGURATION
# ============================================================================

# Prisma Accelerate (Optional - for production scaling)
USE_PRISMA_ACCELERATE=false
PRISMA_ACCELERATE_API_KEY=your-prisma-accelerate-api-key-here

# ============================================================================
# MONITORING & LOGGING
# ============================================================================

# Logging Configuration
LOG_LEVEL=INFO                          # DEBUG | INFO | WARNING | ERROR
LOG_FILE=logs/smartmarket.log

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECKS=true

# ============================================================================
# DEVELOPMENT TOOLS
# ============================================================================

# Development Mode Settings
ENABLE_DEBUG_ROUTES=true                # Enable debug endpoints in development
ENABLE_API_DOCS=true                    # Enable /docs endpoint
ENABLE_CORS_ALL=false                   # Allow all CORS origins (dev only)

# ============================================================================
# SECURITY NOTES
# ============================================================================
# 1. Change all "your-*-here" placeholders with actual values
# 2. Generate secure random keys for JWT_SECRET and ENCRYPTION_MASTER_KEY
# 3. Use environment-specific values for production
# 4. Never commit real credentials to version control
# 5. Always start with DELTA_EXCHANGE_TESTNET=true
# 6. Validate all API keys before production deployment