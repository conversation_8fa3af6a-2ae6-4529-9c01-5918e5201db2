# ML Service Requirements - Use Root Requirements
# This file is deprecated. Use the consolidated requirements.txt in the project root.
#
# To install ML dependencies:
# pip install -r ../requirements.txt
#
# For lightweight ML on M2 MacBook Air:
# pip install -r ../requirements.txt --extra-index-url https://download.pytorch.org/whl/cpu
#
# This file is kept for backward compatibility but should not be used directly.