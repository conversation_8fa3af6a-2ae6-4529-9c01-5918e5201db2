"""
CNN-LSTM Hybrid Model Architecture for cryptocurrency trading with Smart Money Concepts features
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.metrics import precision_score, recall_score, f1_score, accuracy_score
import os
import logging
from typing import Dict, Any, Optional, Tuple, List, Union

from .base_model import BaseModel

logger = logging.getLogger(__name__)

class CNNLSTMModel(BaseModel, nn.Module):
    """
    CNN-LSTM hybrid model for time-series prediction of cryptocurrency markets
    with Smart Money Concepts features.
    
    This model combines CNN layers for feature extraction with LSTM layers
    for temporal modeling. The CNN effectively captures local patterns while
    the LSTM handles temporal dependencies.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        seq_len: int,
        forecast_horizon: int,
        hidden_dim: int = 64,
        num_layers: int = 2,
        kernel_size: int = 3,
        dropout: float = 0.2,
        device: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the CNN-LSTM model.
        
        Args:
            input_dim: Dimension of input features
            output_dim: Number of output units (1 for binary classification)
            seq_len: Length of input sequence
            forecast_horizon: Length of forecast horizon
            hidden_dim: Number of units in LSTM layers
            num_layers: Number of LSTM layers
            kernel_size: Kernel size for CNN layers
            dropout: Dropout rate for regularization
            device: Device to use for computation
            **kwargs: Additional arguments for model initialization
        """
        BaseModel.__init__(self)
        nn.Module.__init__(self)
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.seq_len = seq_len
        self.forecast_horizon = forecast_horizon
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.kernel_size = kernel_size
        self.dropout = dropout
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.cnn = nn.Conv1d(input_dim, hidden_dim, kernel_size=kernel_size, padding=kernel_size//2)
        self.lstm = nn.LSTM(hidden_dim, hidden_dim, num_layers, batch_first=True, dropout=dropout)
        self.fc = nn.Sequential(
            nn.Linear(hidden_dim, output_dim),
            nn.Sigmoid() if output_dim == 1 else nn.Identity()
        )
        self.to(self.device)
        
        if kwargs.get('model_path') and os.path.exists(kwargs['model_path']):
            try:
                self.load_state_dict(torch.load(kwargs['model_path'], map_location=self.device))
                logger.info(f"Loaded weights from {kwargs['model_path']}")
            except Exception as e:
                logger.error(f"Failed to load weights: {e}")
    
    def forward(self, x):
        # Ensure input is 3D: (batch, seq_len, input_dim)
        if x.dim() == 2:
            x = x.unsqueeze(1)  # (batch, 1, input_dim)
        x = x.permute(0, 2, 1)  # (batch, input_dim, seq_len)
        x = self.cnn(x)         # (batch, hidden_dim, seq_len)
        x = x.permute(0, 2, 1)  # (batch, seq_len, hidden_dim)
        out, _ = self.lstm(x)
        if out.dim() == 3:
            out = out[:, -1, :]
        out = self.fc(out)
        return out
    
    def fit_model(
        self,
        train_loader,
        val_loader,
        num_epochs: int = 100,
        lr: float = 0.001,
        early_stopping_patience: int = 10,
        checkpoint_dir: Optional[str] = None,
        class_weights: Optional[torch.Tensor] = None
    ) -> Dict[str, Any]:
        """
        Train the CNN-LSTM model.
        
        Args:
            train_loader: DataLoader for training
            val_loader: DataLoader for validation
            num_epochs: Maximum number of epochs
            lr: Learning rate for optimizer
            early_stopping_patience: Patience for early stopping
            checkpoint_dir: Directory for model checkpoints
            class_weights: Optional tensor of class weights for the loss function
            
        Returns:
            Dictionary with training history
        """
        if self.output_dim == 1:
            criterion = nn.BCEWithLogitsLoss(pos_weight=class_weights.to(self.device) if class_weights is not None else None)
        else:
            criterion = nn.CrossEntropyLoss(weight=class_weights.to(self.device) if class_weights is not None else None)
        optimizer = optim.Adam(self.parameters(), lr=lr)
        best_val_loss = float('inf')
        patience_counter = 0
        best_state = None
        for epoch in range(num_epochs):
            self.train()
            train_losses = []
            for X, y in train_loader:
                X = X.to(self.device).float()
                y = y.to(self.device).float() if self.output_dim == 1 else y.to(self.device).long()
                optimizer.zero_grad()
                outputs = self(X)
                if self.output_dim == 1:
                    outputs = outputs.squeeze(-1)
                    loss = criterion(outputs, y)
                else:
                    loss = criterion(outputs, y)
                loss.backward()
                optimizer.step()
                train_losses.append(loss.item())
            val_loss = self._evaluate_loss(val_loader, criterion)
            logger.info(f"Epoch {epoch+1}/{num_epochs} - Train Loss: {np.mean(train_losses):.4f} - Val Loss: {val_loss:.4f}")
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_state = self.state_dict()
                if checkpoint_dir:
                    os.makedirs(checkpoint_dir, exist_ok=True)
                    torch.save(self.state_dict(), os.path.join(checkpoint_dir, "best_cnnlstm.pt"))
            else:
                patience_counter += 1
                if patience_counter >= early_stopping_patience:
                    logger.info("Early stopping triggered.")
                    break
        if best_state:
            self.load_state_dict(best_state)
        return {"best_val_loss": best_val_loss}
    
    def _evaluate_loss(self, loader, criterion):
        self.eval()
        losses = []
        with torch.no_grad():
            for X, y in loader:
                X = X.to(self.device).float()
                y = y.to(self.device).float() if self.output_dim == 1 else y.to(self.device).long()
                outputs = self(X)
                if self.output_dim == 1:
                    outputs = outputs.squeeze(-1)
                    loss = criterion(outputs, y)
                else:
                    loss = criterion(outputs, y)
                losses.append(loss.item())
        return np.mean(losses)
    
    def predict(self, X: np.ndarray, batch_size: int = 64) -> np.ndarray:
        """
        Make predictions with the model.
        
        Args:
            X: Input data
            batch_size: Batch size for prediction
            
        Returns:
            Predictions
        """
        self.eval()
        preds = []
        with torch.no_grad():
            for i in range(0, len(X), batch_size):
                batch = torch.tensor(X[i:i+batch_size]).to(self.device).float()
                out = self(batch)
                if self.output_dim == 1:
                    out = out.cpu().numpy().flatten()
                else:
                    out = torch.softmax(out, dim=1).cpu().numpy()
                preds.append(out)
        if self.output_dim == 1:
            return np.concatenate(preds)
        else:
            return np.vstack(preds)
    
    def evaluate(self, X: np.ndarray, y: np.ndarray, batch_size: int = 64) -> dict:
        """
        Evaluate the model performance.
        
        Args:
            X: Input data
            y: True labels
            batch_size: Batch size for prediction
            
        Returns:
            Dictionary with evaluation metrics
        """
        y_pred = self.predict(X, batch_size)
        if self.output_dim == 1:
            y_pred_label = (y_pred > 0.5).astype(int)
            y_true = y.astype(int).flatten()
            metrics = {
                'accuracy': accuracy_score(y_true, y_pred_label),
                'precision': precision_score(y_true, y_pred_label),
                'recall': recall_score(y_true, y_pred_label),
                'f1_score': f1_score(y_true, y_pred_label)
            }
        else:
            y_pred_label = np.argmax(y_pred, axis=1)
            y_true = y.astype(int).flatten()
            metrics = {
                'accuracy': accuracy_score(y_true, y_pred_label),
                'precision': precision_score(y_true, y_pred_label, average='macro'),
                'recall': recall_score(y_true, y_pred_label, average='macro'),
                'f1_score': f1_score(y_true, y_pred_label, average='macro')
            }
        return metrics
    
    def save(self, model_path: str) -> None:
        """
        Save the model.
        
        Args:
            model_path: Path to save the model
        """
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        torch.save(self.state_dict(), model_path)
        logger.info(f"Model saved to {model_path}")
    
    @classmethod
    def load(cls, model_path: str, **kwargs) -> 'CNNLSTMModel':
        """
        Load a pre-trained model.
        
        Args:
            model_path: Path to the saved model
            **kwargs: Additional arguments for model initialization
            
        Returns:
            Loaded model
        """
        model = cls(**kwargs)
        model.load_state_dict(torch.load(model_path, map_location=kwargs.get('device', 'cpu')))
        model.eval()
        return model
    
    def visualize_feature_maps(self, X: np.ndarray, layer_names: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
        """
        Visualize feature maps from CNN layers.
        
        Args:
            X: Input data
            layer_names: Names of layers to visualize
            
        Returns:
            Dictionary mapping layer names to feature maps
        """
        if layer_names is None:
            # Get all convolutional layer names
            layer_names = [layer.name for layer in self.modules() if isinstance(layer, nn.Conv1d)]
        
        # Create models to output feature maps
        feature_maps = {}
        for layer_name in layer_names:
            layer = next((l for l in self.modules() if l.__class__.__name__ == layer_name), None)
            if layer is not None:
                intermediate_model = nn.Sequential(
                    layer,
                    nn.Flatten()
                )
                # Get feature maps for the first sample
                feature_maps[layer_name] = intermediate_model(torch.tensor(X[:1]).permute(0, 2, 1)).cpu().numpy()
                
        return feature_maps
    
    def get_feature_importance(self, X: np.ndarray, n_samples: int = 10) -> Dict[str, float]:
        """
        Get feature importance using permutation importance.
        
        Args:
            X: Input data
            n_samples: Number of samples to use
            
        Returns:
            Dictionary mapping feature indices to importance scores
        """
        if len(X) < n_samples:
            n_samples = len(X)
            
        # Select random samples
        indices = np.random.choice(len(X), n_samples, replace=False)
        X_samples = X[indices]
        
        # Get baseline predictions
        baseline_preds = self.predict(X_samples)
        
        # Initialize importance dict
        importance_dict = {}
        
        # For each feature, permute its values and measure impact
        for feature_idx in range(X_samples.shape[2]):
            # Create permuted data
            X_permuted = X_samples.copy()
            
            # Permute the feature across all time steps
            for i in range(X_samples.shape[0]):
                perm_indices = np.random.permutation(X_samples.shape[1])
                X_permuted[i, :, feature_idx] = X_samples[i, perm_indices, feature_idx]
            
            # Get predictions with permuted feature
            permuted_preds = self.predict(X_permuted)
            
            # Calculate importance as mean absolute difference
            importance = np.mean(np.abs(baseline_preds - permuted_preds))
            importance_dict[feature_idx] = float(importance)
        
        # Sort by importance (descending)
        importance_dict = dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))
        
        return importance_dict

    def _build_model(self):
        pass 