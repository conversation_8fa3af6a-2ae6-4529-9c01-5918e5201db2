version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-smoops}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: smartmarket-backend
    volumes:
      - ./backend:/app
      - /app/node_modules
    ports:
      - "${BACKEND_PORT:-3005}:3005"
    env_file:
      - .env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-smoops}?schema=public
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3005
    depends_on:
      postgres:
        condition: service_healthy
    command: npm run dev
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3005/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: dev
    container_name: smartmarket-frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    env_file:
      - .env
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3005
      - NEXT_PUBLIC_WS_URL=ws://backend:3005/ws
      - NODE_ENV=development
    depends_on:
      - backend
      - ml-system
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3



  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    restart: always
    ports:
      - "11434:11434"
    volumes:
      - /Volumes/abhaskumarrr_ssd/ollama_models:/root/.ollama
    environment:
      - OLLAMA_MODELS=/root/.ollama
    # Pulls models to external SSD for persistence and performance
    # To use a different model, set OLLAMA_DEFAULT_MODEL=phi4 or phi3 as needed

  questdb:
    image: questdb/questdb:7.3.10
    container_name: questdb
    ports:
      - "${QUESTDB_HTTP_PORT:-9000}:9000"  # HTTP/REST API and Web Console
      - "${QUESTDB_PG_PORT:-8812}:8812"    # PostgreSQL wire protocol
      - "${QUESTDB_ILP_PORT:-9009}:9009"   # InfluxDB Line Protocol (high-performance ingestion)
    volumes:
      - questdb_data:/var/lib/questdb
    environment:
      - QDB_CAIRO_COMMIT_LAG=1000
      - QDB_CAIRO_MAX_UNCOMMITTED_ROWS=10000
      - QDB_SHARED_WORKER_COUNT=2
      - QDB_HTTP_WORKER_COUNT=2
      - QDB_HTTP_WORKER_AFFINITY=false
      - QDB_HTTP_WORKER_HALTONFAILURE=false
      - QDB_PG_ENABLED=true
      - QDB_PG_NET_ACTIVE_CONNECTION_LIMIT=10
      - QDB_LINE_TCP_ENABLED=true
      - QDB_LINE_TCP_NET_ACTIVE_CONNECTION_LIMIT=10
      - QDB_TELEMETRY_ENABLED=false
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9000/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped

  # Redis for caching and real-time data
  redis:
    image: redis:7-alpine
    container_name: smartmarket-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # ML Trading System
  ml-system:
    build:
      context: .
      dockerfile: docker/Dockerfile.ml-system
    container_name: smartmarket-ml-system
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-smoops}?schema=public
      - REDIS_URL=redis://redis:6379/0
      - DELTA_EXCHANGE_API_KEY=${DELTA_EXCHANGE_API_KEY}
      - DELTA_EXCHANGE_SECRET=${DELTA_EXCHANGE_SECRET}
      - DELTA_EXCHANGE_TESTNET=${DELTA_EXCHANGE_TESTNET:-true}
      - LOG_LEVEL=INFO
      - ENVIRONMENT=development
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
      - ./data:/app/data
      - .:/app
    ports:
      - "${ML_PORT:-8000}:8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3



  # Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    container_name: smartmarket-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: smartmarket-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
  questdb_data:
  redis_data:
  prometheus_data:
  grafana_data:
