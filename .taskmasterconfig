{"models": {"main": {"provider": "openrouter", "modelId": "deepseek/deepseek-chat-v3-0324:free", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "thudm/glm-4-32b:free", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "mistralai/mistral-small-3.1-24b-instruct:free", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}