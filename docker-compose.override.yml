# Docker Compose Override for Development
# This file extends docker-compose.yml for development-specific configurations
# Automatically loaded when running docker-compose commands

version: '3.8'

services:
  backend:
    # Override for development with hot reloading
    volumes:
      - ./backend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=true
      - LOG_LEVEL=debug
    # Enable hot reloading
    command: npm run dev
    # Development port mapping
    ports:
      - "3005:3005"

  frontend:
    # Override for development with hot reloading
    build:
      target: dev  # Use development stage
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3005
      - NEXT_PUBLIC_ML_URL=http://localhost:8000
      - FAST_REFRESH=true
    # Enable hot reloading
    command: npm run dev
    # Development port mapping
    ports:
      - "3000:3000"

  ml-system:
    # Override for development with hot reloading
    volumes:
      - .:/app
      - ./models:/app/models
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    # Development port mapping
    ports:
      - "8000:8000"

  postgres:
    # Development database with persistent data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=smoops
    ports:
      - "5432:5432"
    # Add development-specific configuration
    command: postgres -c log_statement=all -c log_destination=stderr

  redis:
    # Development Redis with persistent data
    ports:
      - "6379:6379"
    # Enable Redis logging for development
    command: redis-server --appendonly yes --loglevel verbose

  questdb:
    # Development QuestDB with additional logging
    environment:
      - QDB_LOG_W_STDOUT_LEVEL=INFO
      - QDB_LOG_W_FILE_LEVEL=DEBUG
    ports:
      - "9000:9000"
      - "8812:8812"
      - "9009:9009"

# Development-specific networks
networks:
  default:
    name: smartmarket-dev
    driver: bridge

# Development volumes with better performance on macOS
volumes:
  postgres_data:
    driver: local
  questdb_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
