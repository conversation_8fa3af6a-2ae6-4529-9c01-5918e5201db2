# Task ID: 4
# Title: Develop Trading Decision Engine
# Status: done
# Dependencies: 2, 3
# Priority: high
# Description: Build the core logic for translating confluence scores and momentum trains into trade decisions.
# Details:
Implement entry logic (75%+ confluence + momentum train) and exit logic (trend reversal + candle formation). Use `pandas` for data manipulation and `numpy` for calculations.

# Test Strategy:
Backtest the decision engine with historical data to verify accuracy.

# Subtasks:
## 1. Develop Entry Logic [done]
### Dependencies: None
### Description: Create the logic for determining precise entry points for trades.
### Details:
Integrate Fi<PERSON>acci, SMC, confluence, and candle formation analysis for ML model features.

## 2. Develop Exit Logic [done]
### Dependencies: None
### Description: Create the logic for determining precise exit points for trades.
### Details:
Ensure the logic is optimized for small capital with high leverage trading.

## 3. Implement Data Manipulation [done]
### Dependencies: None
### Description: Prepare and manipulate data for use in the ML models.
### Details:
Ensure data is clean, normalized, and ready for feature extraction.

## 4. Perform Calculations [done]
### Dependencies: 4.1, 4.2, 4.3
### Description: Conduct necessary calculations for trading decisions.
### Details:
Include calculations for risk management, profit targets, and stop-loss levels.

## 5. Conduct Backtesting [done]
### Dependencies: 4.4
### Description: Test the trading decision engine using historical data.
### Details:
Evaluate the performance of the entry/exit logic and calculations.

