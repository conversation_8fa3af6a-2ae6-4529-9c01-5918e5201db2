{"name": "SMOOPs_dev", "version": "1.0.0", "description": "Smart Money Order Blocks Trading Bot", "main": "index.js", "repository": "https://github.com/abhaskumarrr/SMOOPs_dev", "author": "<PERSON><PERSON><PERSON> <109647440+a<PERSON><PERSON><EMAIL>>", "license": "MIT", "private": true, "workspaces": ["backend", "frontend", "ml"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:ml\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:ml": "cd ml && PYTHONPATH=. python3 backend/src/scripts/server.py", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\" \"npm run start:ml\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "start:ml": "cd ml && PYTHONPATH=. python3 backend/src/scripts/server.py", "setup": "bash scripts/setup-env.sh", "dev:setup": "bash scripts/dev-setup.sh", "dev:tasks": "bash scripts/dev-tasks.sh", "check-env": "node scripts/check-env.js", "generate-key": "node scripts/generate-encryption-key.js", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "docker:build": "docker-compose build", "db:migrate": "cd backend && npm run prisma:migrate:dev", "db:generate": "cd backend && npm run prisma:generate", "db:reset": "cd backend && npx prisma migrate reset --force", "test": "bash scripts/dev-tasks.sh test", "test:backend": "bash scripts/dev-tasks.sh test:backend", "test:frontend": "bash scripts/dev-tasks.sh test:frontend", "test:ml": "bash scripts/dev-tasks.sh test:ml", "lint": "bash scripts/dev-tasks.sh lint", "lint:fix": "bash scripts/dev-tasks.sh lint:fix", "clean": "bash scripts/dev-tasks.sh clean", "compliance:report": "node backend/scripts/compliance_report.js", "ml-trading": "cd backend && npx ts-node src/scripts/start-ml-trading.ts", "ml-trading:paper": "cd backend && npx ts-node src/scripts/start-ml-trading.ts -- --paper", "ml-trading:live": "cd backend && npx ts-node src/scripts/start-ml-trading.ts -- --live", "ml-trading:conservative": "cd backend && npx ts-node src/scripts/start-ml-trading.ts -- --conservative"}, "devDependencies": {"@types/dotenv": "^8.2.0", "@types/node": "^22.15.21", "@types/uuid": "^10.0.0", "chalk": "^4.1.2", "concurrently": "^8.2.2", "dotenv": "^16.5.0", "eslint": "^9.17.0"}, "engines": {"node": ">=24.1.0"}, "dependencies": {"next": "^15.1.8", "task-master-ai": "^0.15.0", "taskmaster": "^0.0.3", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "undici": "6.20.1"}, "overrides": {"rimraf": "^5.0.0", "glob": "^11.0.0", "@humanwhocodes/config-array": "@eslint/config-array", "@humanwhocodes/object-schema": "@eslint/object-schema"}}