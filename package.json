{"name": "SMOOPs_dev", "version": "1.0.0", "description": "Smart Money Order Blocks Trading Bot", "main": "index.js", "repository": "https://github.com/abhaskumarrr/SMOOPs_dev", "author": "<PERSON><PERSON><PERSON> <109647440+a<PERSON><PERSON><EMAIL>>", "license": "MIT", "private": true, "workspaces": ["backend", "frontend", "ml"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:ml\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:ml": "python3 main.py", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\" \"npm run start:ml\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "start:ml": "python3 main.py", "setup": "bash scripts/setup-env.sh", "check-env": "node scripts/check-env.js", "generate-key": "node scripts/generate-encryption-key.js", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "docker:build": "docker-compose build", "db:migrate": "cd backend && npm run prisma:migrate:dev", "db:generate": "cd backend && npm run prisma:generate", "db:reset": "cd backend && npx prisma migrate reset --force", "test": "cd backend && npm test", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:ml": "python3 -m pytest ml/tests/", "lint": "cd backend && npm run lint && cd ../frontend && npm run lint", "lint:fix": "cd backend && npm run lint:fix && cd ../frontend && npm run lint:fix", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules ml/__pycache__ __pycache__", "ml-trading": "cd backend && npx ts-node src/scripts/start-ml-trading.ts", "ml-trading:paper": "cd backend && npx ts-node src/scripts/start-ml-trading.ts -- --paper", "ml-trading:live": "cd backend && npx ts-node src/scripts/start-ml-trading.ts -- --live"}, "devDependencies": {"@types/dotenv": "^8.2.0", "@types/node": "^22.15.21", "@types/uuid": "^10.0.0", "chalk": "^4.1.2", "concurrently": "^8.2.2", "dotenv": "^16.5.0", "eslint": "^9.17.0"}, "engines": {"node": ">=24.1.0"}, "dependencies": {"next": "^15.1.8", "task-master-ai": "^0.15.0", "taskmaster": "^0.0.3", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "undici": "6.20.1"}, "overrides": {"rimraf": "^5.0.0", "glob": "^11.0.0", "@humanwhocodes/config-array": "@eslint/config-array", "@humanwhocodes/object-schema": "@eslint/object-schema"}}