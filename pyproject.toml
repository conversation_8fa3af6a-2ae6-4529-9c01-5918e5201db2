[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smoops"
version = "1.0.0"
description = "Smart Money Order Blocks Trading Bot"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}

[tool.black]
line-length = 100
target-version = ["py310"]
include = '\.pyi?$'
exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | node_modules
  )/
)
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
skip_glob = ["**/node_modules/**", "**/dist/**", "**/.venv/**"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
strict_optional = true

[[tool.mypy.overrides]]
module = ["torch.*", "numpy.*", "pandas.*", "matplotlib.*", "seaborn.*", "sklearn.*"]
ignore_missing_imports = true

[tool.ruff]
target-version = "py310"
line-length = 100

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "B",  # flake8-bugbear
    "I",  # isort
    "W",  # pycodestyle warnings
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "N",  # pep8-naming
    "C90", # mccabe complexity
    "S",  # flake8-bandit (security)
]
ignore = [
    "E501",  # line too long (handled by black)
    "B008",  # do not perform function calls in argument defaults
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]  # unused imports
"tests/**/*.py" = ["S101"]  # use of assert in tests

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "slow: marks tests as slow tests"
]
addopts = "--cov=ml --cov-report=term-missing" 