# Production Docker Compose Configuration
# SmartMarketOOPS Trading Platform

version: '3.8'

services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: smartmarket-frontend-prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:3005/api
      - NEXT_PUBLIC_WS_URL=ws://backend:3001
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - smartmarket-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: smartmarket-backend-prod
    ports:
      - "3005:3005"
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3005
      - WS_PORT=3001
      - DELTA_API_KEY=${DELTA_API_KEY}
      - DELTA_API_SECRET=${DELTA_API_SECRET}
      - DELTA_TESTNET=true
    volumes:
      - ./backend/data:/app/data
      - ./backend/logs:/app/logs
    restart: unless-stopped
    networks:
      - smartmarket-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: smartmarket-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - smartmarket-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: smartmarket-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - smartmarket-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: smartmarket-prometheus-prod
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - smartmarket-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: smartmarket-grafana-prod
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - smartmarket-network

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  smartmarket-network:
    driver: bridge
