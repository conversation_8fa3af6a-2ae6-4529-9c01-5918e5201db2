# Docker Environment Configuration
# Copy this file to .env and update with your actual values

# Application Ports
FRONTEND_PORT=3000
BACKEND_PORT=3005
ML_PORT=8000

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=smoops
POSTGRES_PORT=5432
DATABASE_URL=********************************************/smoops?schema=public

# Redis Configuration
REDIS_PORT=6379
REDIS_URL=redis://redis:6379/0

# QuestDB Configuration
QUESTDB_HTTP_PORT=9000
QUESTDB_PG_PORT=8812
QUESTDB_ILP_PORT=9009

# Monitoring Ports
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin

# Delta Exchange API (Required for trading)
DELTA_EXCHANGE_API_KEY=your_api_key_here
DELTA_EXCHANGE_SECRET=your_secret_here
DELTA_EXCHANGE_TESTNET=true

# Application Environment
NODE_ENV=development
LOG_LEVEL=info

# Next.js Configuration
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_API_URL=http://localhost:3005
NEXT_PUBLIC_ML_URL=http://localhost:8000

# Security (Generate secure random strings for production)
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Optional: External Services
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here

# Docker-specific settings
COMPOSE_PROJECT_NAME=smartmarket
COMPOSE_FILE=docker-compose.yml:docker-compose.override.yml
