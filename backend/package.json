{"scripts": {"prisma:migrate:dev": "prisma migrate dev --name init", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:generate": "prisma generate", "prisma:seed": "prisma db seed", "ci:migrate": "prisma migrate deploy && prisma generate", "ci:db-health": "node src/scripts/checkDbHealth.js", "questdb:migrate": "ts-node src/scripts/questdb-migrate.ts migrate", "questdb:migrate:dry-run": "ts-node src/scripts/questdb-migrate.ts migrate --dry-run", "questdb:status": "ts-node src/scripts/questdb-migrate.ts status", "questdb:validate": "ts-node src/scripts/questdb-migrate.ts validate", "questdb:test": "ts-node src/scripts/questdb-migrate.ts test-insert", "questdb:query-test": "ts-node src/scripts/questdb-migrate.ts query-test", "events:start": "ts-node src/scripts/event-driven-system.ts start", "events:stop": "ts-node src/scripts/event-driven-system.ts stop", "events:status": "ts-node src/scripts/event-driven-system.ts status", "events:validate": "ts-node src/scripts/event-driven-system.ts validate", "events:streams": "ts-node src/scripts/event-driven-system.ts streams --list", "events:test": "ts-node src/scripts/event-driven-system.ts test-events", "events:monitor": "ts-node src/scripts/event-driven-system.ts monitor", "test:systems": "ts-node src/scripts/test-systems.ts", "test:infrastructure": "ts-node src/scripts/test-real-infrastructure.ts", "test:infrastructure:optimized": "ts-node src/scripts/test-infrastructure-optimized.ts", "backtest": "ts-node src/scripts/run-backtest.ts", "backtest:simple": "ts-node src/scripts/run-backtest-simple.ts", "backtest:compare": "ts-node src/scripts/run-strategy-comparison.ts", "backtest:trending": "ts-node src/scripts/test-trending-market.ts", "backtest:intelligent": "ts-node src/scripts/run-intelligent-backtest.ts", "optimize": "ts-node src/scripts/run-hyperparameter-optimization.ts", "optimize:fast": "ts-node src/scripts/run-hyperparameter-optimization.ts --iterations=50", "optimize:comprehensive": "ts-node src/scripts/run-hyperparameter-optimization.ts --iterations=200", "analyze:strategy": "ts-node src/scripts/analyze-strategy-performance.ts", "test:fixed": "ts-node src/scripts/test-fixed-strategy.ts", "backtest:multi-tf": "ts-node src/scripts/run-multi-timeframe-backtest.ts", "backtest:multi-tf-fast": "ts-node src/scripts/run-multi-timeframe-backtest.ts --fast", "test:real-data": "ts-node src/scripts/test-real-data.ts", "retrain:models": "ts-node src/scripts/retrain-ai-models.ts", "backtest:retrained": "ts-node src/scripts/run-retrained-model-backtest.ts", "retrain:multi-asset": "ts-node src/scripts/retrain-multi-asset-models.ts", "backtest:multi-asset": "ts-node src/scripts/run-multi-asset-backtest.ts", "backtest:multi-timeframe-multi-asset": "ts-node src/scripts/run-multi-timeframe-multi-asset-backtest.ts", "analyze:real-trading": "ts-node src/scripts/run-real-trading-analysis.ts", "trade:ultra-aggressive": "ts-node src/scripts/run-ultra-aggressive-trading.ts", "test:direct-trading": "ts-node src/scripts/test-direct-trading.ts", "trade:working-system": "ts-node src/scripts/run-working-trading-system.ts", "trade:ultra-high-leverage": "ts-node src/scripts/run-ultra-high-leverage-trading.ts", "trade:real-delta-3month": "ts-node src/scripts/run-real-delta-3month-backtest.ts", "trade:fast-3month": "ts-node src/scripts/run-fast-3month-backtest.ts", "trade:dynamic-takeprofit": "ts-node src/scripts/run-dynamic-takeprofit-backtest.ts", "test:delta-connection": "ts-node src/scripts/test-delta-connection-trades.ts", "trade:paper": "ts-node src/scripts/run-paper-trading.ts", "trade:paper-enhanced": "ts-node src/scripts/start-paper-trading.ts", "trade:real": "ts-node src/scripts/start-real-trading.ts", "ai:manage": "ts-node src/scripts/run-ai-position-manager.ts", "ai:simulate": "ts-node src/scripts/simulate-position-management.ts", "test:full-system": "ts-node src/scripts/full-system-test-and-order.ts", "test:delta-india": "ts-node src/scripts/delta-india-integration-test.ts", "test:delta-direct": "ts-node src/scripts/direct-delta-india-test.ts", "test:delta-final": "ts-node src/scripts/final-delta-india-test.ts", "trade:delta-testnet": "ts-node scripts/start-delta-testnet-trading.ts", "trade:delta-paper": "ts-node scripts/delta-paper-trading.ts", "trade:delta-live": "node scripts/delta-live-trading.js", "infrastructure:setup": "../scripts/setup-infrastructure.sh", "infrastructure:start": "../scripts/setup-infrastructure.sh start", "infrastructure:stop": "../scripts/setup-infrastructure.sh stop", "infrastructure:status": "../scripts/setup-infrastructure.sh status", "dev": "nodemon", "dev:js": "nodemon start-server.js", "build": "tsc", "start": "node dist/server.js", "start:js": "node start-server.js", "start:ts": "ts-node --transpile-only src/server.ts", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:risk": "./scripts/test-risk-management.sh", "perf:monitor": "ts-node src/scripts/performance-monitor.ts", "perf:load-test": "ts-node src/scripts/load-test.ts", "perf:memory": "ts-node src/scripts/memory-analysis.ts", "perf:db-analyze": "ts-node src/scripts/database-analysis.ts", "cache:flush": "ts-node src/scripts/cache-management.ts flush", "cache:stats": "ts-node src/scripts/cache-management.ts stats", "optimize:db": "ts-node src/scripts/optimize-database.ts", "optimize:queries": "ts-node src/scripts/analyze-slow-queries.ts"}, "name": "backend", "version": "1.0.0", "main": "dist/server.js", "keywords": [], "author": "", "license": "ISC", "description": "", "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@prisma/client": "^6.8.2", "@prisma/extension-accelerate": "^1.3.0", "@questdb/nodejs-client": "^3.0.0", "@socket.io/admin-ui": "^0.5.1", "@types/cookie-parser": "^1.4.8", "@types/express-rate-limit": "^5.1.3", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "ccxt": "^4.4.88", "commander": "^12.1.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^7.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.3", "pg": "^8.16.0", "prisma": "^6.8.2", "socket.io": "^4.7.4", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "validator": "^13.11.0", "ws": "^8.16.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.30", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "jest-junit": "^16.0.0", "nock": "^14.0.0", "nodemon": "^3.0.2", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "yn": "^3.1.1"}}