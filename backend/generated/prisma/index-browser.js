
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  role: 'role',
  isVerified: 'isVerified',
  verificationToken: 'verificationToken',
  verificationTokenExpiry: 'verificationTokenExpiry',
  resetToken: 'resetToken',
  resetTokenExpiry: 'resetTokenExpiry',
  lastLoginAt: 'lastLoginAt',
  oauthProvider: 'oauthProvider',
  oauthId: 'oauthId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  token: 'token',
  refreshToken: 'refreshToken',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  deviceId: 'deviceId',
  isValid: 'isValid',
  expiresAt: 'expiresAt',
  lastActiveAt: 'lastActiveAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  rememberMe: 'rememberMe',
  metadata: 'metadata'
};

exports.Prisma.ApiKeyScalarFieldEnum = {
  id: 'id',
  key: 'key',
  encryptedData: 'encryptedData',
  userId: 'userId',
  name: 'name',
  scopes: 'scopes',
  expiry: 'expiry',
  environment: 'environment',
  createdAt: 'createdAt',
  lastUsedAt: 'lastUsedAt',
  isRevoked: 'isRevoked',
  revokedAt: 'revokedAt',
  revokedBy: 'revokedBy',
  revokedReason: 'revokedReason',
  ipRestrictions: 'ipRestrictions',
  hashedSecret: 'hashedSecret',
  usageCount: 'usageCount',
  isDefault: 'isDefault',
  rateLimits: 'rateLimits',
  metadata: 'metadata'
};

exports.Prisma.TradeLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  instrument: 'instrument',
  amount: 'amount',
  price: 'price',
  timestamp: 'timestamp',
  orderId: 'orderId',
  type: 'type',
  status: 'status'
};

exports.Prisma.BotScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  symbol: 'symbol',
  strategy: 'strategy',
  timeframe: 'timeframe',
  parameters: 'parameters',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PositionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  botId: 'botId',
  symbol: 'symbol',
  side: 'side',
  entryPrice: 'entryPrice',
  currentPrice: 'currentPrice',
  amount: 'amount',
  leverage: 'leverage',
  takeProfitPrice: 'takeProfitPrice',
  stopLossPrice: 'stopLossPrice',
  status: 'status',
  pnl: 'pnl',
  openedAt: 'openedAt',
  closedAt: 'closedAt',
  metadata: 'metadata'
};

exports.Prisma.MetricScalarFieldEnum = {
  id: 'id',
  name: 'name',
  value: 'value',
  recordedAt: 'recordedAt',
  tags: 'tags'
};

exports.Prisma.TradingSignalScalarFieldEnum = {
  id: 'id',
  symbol: 'symbol',
  type: 'type',
  direction: 'direction',
  strength: 'strength',
  timeframe: 'timeframe',
  price: 'price',
  targetPrice: 'targetPrice',
  stopLoss: 'stopLoss',
  confidenceScore: 'confidenceScore',
  expectedReturn: 'expectedReturn',
  expectedRisk: 'expectedRisk',
  riskRewardRatio: 'riskRewardRatio',
  generatedAt: 'generatedAt',
  expiresAt: 'expiresAt',
  source: 'source',
  metadata: 'metadata',
  predictionValues: 'predictionValues',
  validatedAt: 'validatedAt',
  validationStatus: 'validationStatus',
  validationReason: 'validationReason',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RiskSettingsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId',
  botId: 'botId',
  isActive: 'isActive',
  positionSizingMethod: 'positionSizingMethod',
  riskPercentage: 'riskPercentage',
  maxPositionSize: 'maxPositionSize',
  kellyFraction: 'kellyFraction',
  winRate: 'winRate',
  customSizingParams: 'customSizingParams',
  stopLossType: 'stopLossType',
  stopLossValue: 'stopLossValue',
  trailingCallback: 'trailingCallback',
  trailingStep: 'trailingStep',
  timeLimit: 'timeLimit',
  stopLossLevels: 'stopLossLevels',
  takeProfitType: 'takeProfitType',
  takeProfitValue: 'takeProfitValue',
  trailingActivation: 'trailingActivation',
  takeProfitLevels: 'takeProfitLevels',
  maxRiskPerTrade: 'maxRiskPerTrade',
  maxRiskPerSymbol: 'maxRiskPerSymbol',
  maxRiskPerDirection: 'maxRiskPerDirection',
  maxTotalRisk: 'maxTotalRisk',
  maxDrawdown: 'maxDrawdown',
  maxPositions: 'maxPositions',
  maxDailyLoss: 'maxDailyLoss',
  cooldownPeriod: 'cooldownPeriod',
  volatilityLookback: 'volatilityLookback',
  circuitBreakerEnabled: 'circuitBreakerEnabled',
  maxDailyLossBreaker: 'maxDailyLossBreaker',
  maxDrawdownBreaker: 'maxDrawdownBreaker',
  volatilityMultiplier: 'volatilityMultiplier',
  consecutiveLossesBreaker: 'consecutiveLossesBreaker',
  tradingPause: 'tradingPause',
  marketWideEnabled: 'marketWideEnabled',
  enableManualOverride: 'enableManualOverride',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RiskAlertScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  level: 'level',
  message: 'message',
  details: 'details',
  timestamp: 'timestamp',
  acknowledged: 'acknowledged',
  resolvedAt: 'resolvedAt'
};

exports.Prisma.CircuitBreakerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId',
  botId: 'botId',
  type: 'type',
  isGlobal: 'isGlobal',
  isActive: 'isActive',
  threshold: 'threshold',
  recoveryThreshold: 'recoveryThreshold',
  cooldownMinutes: 'cooldownMinutes',
  action: 'action',
  lastTriggered: 'lastTriggered',
  status: 'status',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TradingStrategyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  timeHorizon: 'timeHorizon',
  symbols: 'symbols',
  entryRules: 'entryRules',
  exitRules: 'exitRules',
  positionSizing: 'positionSizing',
  riskManagement: 'riskManagement',
  userId: 'userId',
  isPublic: 'isPublic',
  isActive: 'isActive',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StrategyExecutionScalarFieldEnum = {
  id: 'id',
  strategyId: 'strategyId',
  userId: 'userId',
  status: 'status',
  startedAt: 'startedAt',
  stoppedAt: 'stoppedAt',
  runningTimeMs: 'runningTimeMs',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StrategyExecutionResultScalarFieldEnum = {
  id: 'id',
  executionId: 'executionId',
  symbol: 'symbol',
  entryTime: 'entryTime',
  entryPrice: 'entryPrice',
  exitTime: 'exitTime',
  exitPrice: 'exitPrice',
  direction: 'direction',
  quantity: 'quantity',
  profitLoss: 'profitLoss',
  profitLossPercentage: 'profitLossPercentage',
  status: 'status',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MLModelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  version: 'version',
  modelType: 'modelType',
  symbol: 'symbol',
  timeframe: 'timeframe',
  description: 'description',
  status: 'status',
  accuracy: 'accuracy',
  precision: 'precision',
  recall: 'recall',
  f1Score: 'f1Score',
  trainedAt: 'trainedAt',
  lastUsedAt: 'lastUsedAt',
  trainingId: 'trainingId',
  location: 'location',
  params: 'params',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MLPredictionScalarFieldEnum = {
  id: 'id',
  modelId: 'modelId',
  symbol: 'symbol',
  timeframe: 'timeframe',
  predictionType: 'predictionType',
  values: 'values',
  timestamps: 'timestamps',
  confidenceScores: 'confidenceScores',
  metadata: 'metadata',
  generatedAt: 'generatedAt',
  expiresAt: 'expiresAt',
  signalGenerated: 'signalGenerated',
  signalId: 'signalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MLTrainingJobScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  symbol: 'symbol',
  timeframe: 'timeframe',
  modelType: 'modelType',
  status: 'status',
  progress: 'progress',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  resultModelId: 'resultModelId',
  errorMessage: 'errorMessage',
  params: 'params',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BridgeConfigScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  mlModelId: 'mlModelId',
  autoGenerateSignals: 'autoGenerateSignals',
  confidenceThreshold: 'confidenceThreshold',
  signalExpiryMinutes: 'signalExpiryMinutes',
  refreshIntervalMinutes: 'refreshIntervalMinutes',
  symbols: 'symbols',
  timeframes: 'timeframes',
  lastExecutedAt: 'lastExecutedAt',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PerformanceTestScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  testType: 'testType',
  duration: 'duration',
  concurrency: 'concurrency',
  rampUp: 'rampUp',
  targetEndpoint: 'targetEndpoint',
  modelId: 'modelId',
  strategyId: 'strategyId',
  symbol: 'symbol',
  timeframe: 'timeframe',
  options: 'options',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PerformanceTestResultScalarFieldEnum = {
  id: 'id',
  testId: 'testId',
  status: 'status',
  startTime: 'startTime',
  endTime: 'endTime',
  duration: 'duration',
  metrics: 'metrics',
  errors: 'errors',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OptimizationRecommendationScalarFieldEnum = {
  id: 'id',
  testResultId: 'testResultId',
  category: 'category',
  impact: 'impact',
  description: 'description',
  implementation: 'implementation',
  estimatedImprovement: 'estimatedImprovement',
  createdAt: 'createdAt'
};

exports.Prisma.ABTestScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  variantA: 'variantA',
  variantB: 'variantB',
  type: 'type',
  metric: 'metric',
  targetImprovement: 'targetImprovement',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ABTestResultScalarFieldEnum = {
  id: 'id',
  testId: 'testId',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  variantAMetrics: 'variantAMetrics',
  variantBMetrics: 'variantBMetrics',
  winner: 'winner',
  improvement: 'improvement',
  confidenceLevel: 'confidenceLevel',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PerformanceMetricScalarFieldEnum = {
  id: 'id',
  timestamp: 'timestamp',
  system: 'system',
  component: 'component',
  metric: 'metric',
  value: 'value',
  unit: 'unit',
  tags: 'tags'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  status: 'status',
  symbol: 'symbol',
  type: 'type',
  side: 'side',
  quantity: 'quantity',
  price: 'price',
  stopPrice: 'stopPrice',
  avgFillPrice: 'avgFillPrice',
  filledQuantity: 'filledQuantity',
  remainingQuantity: 'remainingQuantity',
  fee: 'fee',
  feeCurrency: 'feeCurrency',
  clientOrderId: 'clientOrderId',
  exchangeOrderId: 'exchangeOrderId',
  source: 'source',
  exchangeId: 'exchangeId',
  submittedAt: 'submittedAt',
  updatedAt: 'updatedAt',
  completedAt: 'completedAt',
  errorCode: 'errorCode',
  errorMessage: 'errorMessage',
  errorDetails: 'errorDetails',
  raw: 'raw',
  userId: 'userId',
  positionId: 'positionId',
  strategyId: 'strategyId',
  botId: 'botId',
  signalId: 'signalId'
};

exports.Prisma.DecisionLogScalarFieldEnum = {
  id: 'id',
  timestamp: 'timestamp',
  source: 'source',
  actionType: 'actionType',
  decision: 'decision',
  reasonDetails: 'reasonDetails',
  userId: 'userId',
  botId: 'botId',
  strategyId: 'strategyId',
  symbol: 'symbol',
  orderId: 'orderId',
  positionId: 'positionId',
  importance: 'importance',
  metadata: 'metadata',
  tags: 'tags',
  auditTrailId: 'auditTrailId'
};

exports.Prisma.AuditTrailScalarFieldEnum = {
  id: 'id',
  trailType: 'trailType',
  entityId: 'entityId',
  entityType: 'entityType',
  startTime: 'startTime',
  endTime: 'endTime',
  status: 'status',
  summary: 'summary',
  userId: 'userId',
  orderId: 'orderId',
  tags: 'tags',
  metadata: 'metadata'
};

exports.Prisma.AuditEventScalarFieldEnum = {
  id: 'id',
  auditTrailId: 'auditTrailId',
  timestamp: 'timestamp',
  eventType: 'eventType',
  component: 'component',
  action: 'action',
  status: 'status',
  details: 'details',
  dataBefore: 'dataBefore',
  dataAfter: 'dataAfter',
  metadata: 'metadata'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  timestamp: 'timestamp'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  User: 'User',
  Session: 'Session',
  ApiKey: 'ApiKey',
  TradeLog: 'TradeLog',
  Bot: 'Bot',
  Position: 'Position',
  Metric: 'Metric',
  TradingSignal: 'TradingSignal',
  RiskSettings: 'RiskSettings',
  RiskAlert: 'RiskAlert',
  CircuitBreaker: 'CircuitBreaker',
  TradingStrategy: 'TradingStrategy',
  StrategyExecution: 'StrategyExecution',
  StrategyExecutionResult: 'StrategyExecutionResult',
  MLModel: 'MLModel',
  MLPrediction: 'MLPrediction',
  MLTrainingJob: 'MLTrainingJob',
  BridgeConfig: 'BridgeConfig',
  PerformanceTest: 'PerformanceTest',
  PerformanceTestResult: 'PerformanceTestResult',
  OptimizationRecommendation: 'OptimizationRecommendation',
  ABTest: 'ABTest',
  ABTestResult: 'ABTestResult',
  PerformanceMetric: 'PerformanceMetric',
  Order: 'Order',
  DecisionLog: 'DecisionLog',
  AuditTrail: 'AuditTrail',
  AuditEvent: 'AuditEvent',
  AuditLog: 'AuditLog'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
