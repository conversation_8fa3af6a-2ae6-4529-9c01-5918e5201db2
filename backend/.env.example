# SmartMarketOOPS Environment Configuration

# Database Configuration
DATABASE_URL="postgresql://smoops_user:smoops_password@localhost:5432/smartmarketoops"

# QuestDB Configuration (Time-Series Database)
QUESTDB_HOST="localhost"
QUESTDB_PORT="9000"
QUESTDB_ILP_HOST="localhost"
QUESTDB_ILP_PORT="9009"
QUESTDB_PG_PORT="8812"
QUESTDB_HTTP_PORT="9000"
QUESTDB_USERNAME=""
QUESTDB_PASSWORD=""
QUESTDB_ILP_USERNAME=""
QUESTDB_ILP_PASSWORD=""
QUESTDB_ILP_TOKEN=""
QUESTDB_ILP_TLS="false"
QUESTDB_SSL="false"
QUESTDB_DATABASE="qdb"
QUESTDB_CONNECTION_TIMEOUT="30000"
QUESTDB_QUERY_TIMEOUT="60000"
QUESTDB_MAX_CONNECTIONS="10"
QUESTDB_RETRY_ATTEMPTS="3"
QUESTDB_RETRY_DELAY="1000"
QUESTDB_AUTO_FLUSH_ROWS="1000"
QUESTDB_AUTO_FLUSH_INTERVAL="1000"
QUESTDB_MIN_THROUGHPUT="1024"
QUESTDB_REQUEST_TIMEOUT="10000"
QUESTDB_RETRY_TIMEOUT="60000"
QUESTDB_MAX_BUFFER_SIZE="65536"

# Redis Configuration (Event-Driven Architecture)
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""
REDIS_DB="0"
REDIS_KEY_PREFIX="smoops:"
REDIS_RETRY_DELAY="100"
REDIS_MAX_RETRIES="3"
REDIS_KEEP_ALIVE="30000"
REDIS_CONNECT_TIMEOUT="10000"
REDIS_COMMAND_TIMEOUT="5000"
REDIS_CLUSTER_RETRY_DELAY="100"

# Redis Streams Configuration
REDIS_STREAM_MAX_LENGTH="10000"
REDIS_STREAM_APPROXIMATE="true"
REDIS_STREAM_TRIM_STRATEGY="MAXLEN"
REDIS_CONSUMER_GROUP="trading-system"
REDIS_CONSUMER_NAME="consumer-1"
REDIS_BLOCK_TIME="1000"
REDIS_STREAM_COUNT="10"
REDIS_RETRY_INTERVAL="5000"
REDIS_DEAD_LETTER_THRESHOLD="5"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="15m"

# Delta Exchange API Configuration (India Testnet)
DELTA_EXCHANGE_API_KEY="HmerKHhySssgFIAfEIh4CYA5E3VmKg"
DELTA_EXCHANGE_API_SECRET="1YNVg1x9cIjz1g3BPOQPUJQr6LhEm8w7cTaXi8ebJYPUpx5BKCQysMoLd6FT"
DELTA_EXCHANGE_TESTNET="true"

# Market Data Configuration
USE_REAL_MARKET_DATA="true"
MARKET_DATA_UPDATE_INTERVAL="5"
WEBSOCKET_PORT="3001"

# Trading Configuration
ENABLE_PAPER_TRADING="true"
DEFAULT_POSITION_SIZE="0.1"
MAX_DAILY_TRADES="50"

# ML Model Configuration
ML_MODEL_ENDPOINT="http://localhost:8000"
ENABLE_TRANSFORMER_MODEL="true"
ENABLE_ENSEMBLE_MODEL="true"

# Logging Configuration
LOG_LEVEL="info"
ENABLE_DEBUG_LOGS="false"

# Rate Limiting
API_RATE_LIMIT="100"
WEBSOCKET_RATE_LIMIT="1000"

# Security
ENCRYPTION_KEY="your-encryption-key-here"
CORS_ORIGIN="http://localhost:3000"

# Performance
CACHE_TTL="300"
MAX_CONCURRENT_CONNECTIONS="100"