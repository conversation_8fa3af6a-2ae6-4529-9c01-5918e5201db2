{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "declaration": true, "noImplicitAny": false, "noUnusedLocals": false, "noUnusedParameters": false, "ignoreDeprecations": "5.0", "allowJs": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts", "dist"]}