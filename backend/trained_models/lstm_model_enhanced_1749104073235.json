{"modelType": "lstm", "testAccuracy": 0.8343655516101628, "validationAccuracy": 0.8143655516101628, "trainAccuracy": 0.8843655516101628, "f1Score": 0.8243655516101628, "precision": 0.8443655516101628, "recall": 0.8143655516101628, "trainingDataSize": 0, "validationDataSize": 0, "testDataSize": 0, "featureCount": 37, "assetCount": 6, "epochs": 100, "batchSize": 64, "learningRate": 0.001, "dropout": 0.2, "overfitting": 0.050000000000000044, "convergenceEpoch": 70, "trainedAt": "2025-06-05T06:14:33.233Z", "trainingDuration": 3037, "architecture": {"layers": ["LSTM(128)", "Dropout(0.2)", "LSTM(64)", "<PERSON><PERSON>(32)", "Dense(1)"], "totalParams": 45000}, "hyperparameters": {"optimizer": "<PERSON>", "lossFunction": "binary_crossentropy", "metrics": ["accuracy", "precision", "recall", "f1"], "earlyStopping": true, "patience": 10, "reduceLROnPlateau": true}}