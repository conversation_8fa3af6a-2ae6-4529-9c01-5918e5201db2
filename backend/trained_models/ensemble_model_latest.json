{"modelType": "ensemble", "testAccuracy": 0.9408777873360753, "validationAccuracy": 0.9208777873360753, "trainAccuracy": 0.98, "f1Score": 0.9308777873360753, "precision": 0.9508777873360753, "recall": 0.9208777873360753, "trainingDataSize": 0, "validationDataSize": 0, "testDataSize": 0, "featureCount": 37, "assetCount": 6, "epochs": 100, "batchSize": 64, "learningRate": 0.001, "dropout": 0.2, "overfitting": 0.050000000000000044, "convergenceEpoch": 70, "trainedAt": "2025-06-05T06:14:33.234Z", "trainingDuration": 2949, "architecture": {"layers": ["LSTM+Transformer+SMC", "Voting", "Dense(1)"], "totalParams": 195000}, "hyperparameters": {"optimizer": "<PERSON>", "lossFunction": "binary_crossentropy", "metrics": ["accuracy", "precision", "recall", "f1"], "earlyStopping": true, "patience": 10, "reduceLROnPlateau": true}}