#!/usr/bin/env node
/**
 * DELTA EXCHANGE TESTNET LIVE TRADING
 * 
 * Runs our Ultimate Trading Strategy on Delta Exchange testnet
 * Trades will execute and reflect on your Delta Exchange account
 */

require('dotenv').config();

// Simple logging function
const log = (message) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
};

class DeltaTestnetLiveTrading {
  constructor() {
    this.config = {
      // Delta Exchange testnet configuration
      testnet: true,
      apiKey: process.env.DELTA_EXCHANGE_API_KEY,
      apiSecret: process.env.DELTA_EXCHANGE_API_SECRET,
      
      // Trading parameters
      symbols: ['BTCUSD', 'ETHUSD'],
      riskPerTrade: 2.5,        // 2.5% risk per trade
      maxPositions: 2,          // Max 2 concurrent positions
      stopLossPercent: 1.2,     // 1.2% stop loss
      takeProfitRatio: 3.0,     // 3:1 reward ratio
      confluenceThreshold: 0.85, // 85% minimum confluence (temporarily lowered to test position sizing)
      
      // OHLC Zone strategy
      zoneBuffer: 0.15,         // 0.15% zone buffer
      minZoneStrength: 75       // 75% minimum zone strength
    };

    this.isRunning = false;
    this.activePositions = new Map();
    this.dailyLevels = new Map();
    this.performance = {
      totalTrades: 0,
      winningTrades: 0,
      totalPnL: 0,
      winRate: 0
    };
  }

  /**
   * Start live trading on Delta Exchange testnet
   */
  async startLiveTrading() {
    try {
      this.isRunning = true;
      
      log('🚀 STARTING DELTA EXCHANGE TESTNET LIVE TRADING');
      log('═'.repeat(80));
      log('⚠️  TESTNET MODE - Trades will execute on your Delta Exchange testnet account');
      log('📊 Strategy: Daily OHLC Zone Trading');
      log('🎯 Target: 75%+ confluence, 3:1 risk/reward');
      log('═'.repeat(80));
      
      // Validate API credentials
      if (!this.config.apiKey || !this.config.apiSecret) {
        throw new Error('Delta Exchange API credentials not found in .env file');
      }
      
      log('✅ API credentials found');
      log('🔧 Initializing Delta Exchange connection...');
      
      // Initialize Delta Exchange client
      await this.initializeDeltaExchange();
      
      // Get account balance
      await this.updateBalance();
      
      // Calculate initial OHLC levels
      await this.calculateDailyOHLCLevels();
      
      log('✅ System initialized successfully');
      log('🎯 Starting live trading loop...');
      
      // Main trading loop
      let cycleCount = 0;
      while (this.isRunning && cycleCount < 100) { // Limit cycles for demo
        try {
          cycleCount++;
          
          log(`\n📊 Trading Cycle ${cycleCount}`);
          
          // Update balance
          await this.updateBalance();
          
          // Analyze each symbol for trading opportunities
          for (const symbol of this.config.symbols) {
            if (this.activePositions.size >= this.config.maxPositions) {
              log(`⏸️ Maximum positions reached (${this.activePositions.size}/${this.config.maxPositions})`);
              break;
            }
            
            await this.analyzeSymbolForTrade(symbol);
            await this.sleep(5000); // 5 second delay
          }
          
          // Manage active positions
          await this.manageActivePositions();
          
          // Display status
          this.displayTradingStatus();
          
          // Wait before next cycle
          await this.sleep(30000); // 30 second cycle
          
        } catch (error) {
          log(`❌ Error in trading cycle ${cycleCount}: ${error.message}`);
          await this.sleep(30000);
        }
      }
      
      log('🛑 Trading loop completed');
      
    } catch (error) {
      log(`❌ Failed to start live trading: ${error.message}`);
      throw error;
    }
  }

  /**
   * Initialize Delta Exchange client (REAL CONNECTION)
   */
  async initializeDeltaExchange() {
    try {
      log('🔌 Connecting to your Delta Exchange account...');

      // Import the actual Delta Exchange service
      const { DeltaExchangeUnified } = require('../dist/services/DeltaExchangeUnified');

      // Initialize with your actual API credentials
      this.deltaService = new DeltaExchangeUnified({
        apiKey: this.config.apiKey,
        apiSecret: this.config.apiSecret,
        testnet: this.config.testnet
      });

      // Wait for the service to be properly initialized
      log('⏳ Waiting for Delta Exchange service to initialize...');

      // Wait for initialization to complete
      await new Promise((resolve, reject) => {
        if (this.deltaService.isReady()) {
          resolve(true);
        } else {
          this.deltaService.once('initialized', () => resolve(true));
          this.deltaService.once('error', (error) => reject(error));

          // Timeout after 30 seconds
          setTimeout(() => reject(new Error('Initialization timeout')), 30000);
        }
      });

      log('✅ Delta Exchange service initialized successfully');

      // Test connection by getting account info
      const accountInfo = await this.deltaService.getBalance();

      log('✅ Connected to your Delta Exchange account successfully');
      log(`📊 Account currencies: ${accountInfo.map(b => b.asset).join(', ')}`);

      return true;

    } catch (error) {
      log(`❌ Failed to connect to Delta Exchange: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update account balance from YOUR Delta Exchange India account
   */
  async updateBalance() {
    try {
      // Get your actual balance from Delta Exchange India
      const balanceData = await this.deltaService.getBalance();

      log('🔍 Raw balance data from Delta Exchange India:');
      balanceData.forEach(b => {
        log(`   Asset ID: ${b.asset_id}, Symbol: ${b.asset_symbol || 'undefined'}, Balance: ${b.balance}, Available: ${b.available_balance}`);
      });

      // Delta Exchange India uses INR as primary currency
      // Look for INR balance first, then fallback to largest balance
      let primaryBalance = balanceData.find(b =>
        b.asset_symbol === 'INR' ||
        b.asset_symbol === 'USDT' ||
        b.asset_id === 1 // INR typically has asset_id 1
      );

      // If no INR/USDT found, use the balance with highest value
      if (!primaryBalance) {
        primaryBalance = balanceData.reduce((max, current) =>
          parseFloat(current.balance || 0) > parseFloat(max.balance || 0) ? current : max
        , balanceData[0] || { balance: 0, available_balance: 0 });
      }

      this.balance = {
        totalBalance: parseFloat(primaryBalance.balance || 0),
        availableBalance: parseFloat(primaryBalance.available_balance || 0),
        currency: primaryBalance.asset_symbol || 'INR',
        assetId: primaryBalance.asset_id
      };

      const currencySymbol = this.balance.currency === 'INR' ? '₹' : '$';

      log(`💰 YOUR Delta Exchange India Balance: ${currencySymbol}${this.balance.totalBalance.toFixed(2)} ${this.balance.currency}`);
      log(`📊 Available for Trading: ${currencySymbol}${this.balance.availableBalance.toFixed(2)} ${this.balance.currency}`);

      // Show all balances for transparency
      if (balanceData.length > 1) {
        log('💼 All account balances:');
        balanceData.forEach(b => {
          if (parseFloat(b.balance) > 0) {
            const symbol = b.asset_symbol || `Asset-${b.asset_id}`;
            log(`   ${symbol}: ${parseFloat(b.balance).toFixed(2)} (Available: ${parseFloat(b.available_balance || 0).toFixed(2)})`);
          }
        });
      }

    } catch (error) {
      log(`❌ Failed to update balance: ${error.message}`);
      // Don't throw error, continue with last known balance
    }
  }

  /**
   * Calculate daily OHLC levels from YOUR Delta Exchange data
   */
  async calculateDailyOHLCLevels() {
    try {
      log('📊 Getting real OHLC data from your Delta Exchange...');

      // Delta Exchange TESTNET product IDs for perpetual futures
      const productIds = {
        'BTCUSD': 84,    // BTC perpetual (testnet)
        'ETHUSD': 1699   // ETH perpetual (testnet)
      };

      for (const symbol of this.config.symbols) {
        try {
          const productId = productIds[symbol];
          if (!productId) {
            log(`⚠️ Product ID not found for ${symbol}, skipping...`);
            continue;
          }

          // Get real candle data from Delta Exchange
          const candles = await this.deltaService.getCandles(productId, '1d', 2);

          if (candles && candles.length >= 2) {
            const previousDay = candles[candles.length - 2];

            const PDH = parseFloat(previousDay.high);
            const PDL = parseFloat(previousDay.low);
            const PDC = parseFloat(previousDay.close);
            const PDO = parseFloat(previousDay.open);
            const PP = (PDH + PDL + PDC) / 3;

            this.dailyLevels.set(symbol, { PDH, PDL, PDC, PDO, PP });

            log(`📈 ${symbol} REAL OHLC Levels from Delta Exchange:`);
            log(`   PDH: $${PDH.toFixed(2)} | PDL: $${PDL.toFixed(2)} | PDC: $${PDC.toFixed(2)}`);
            log(`   Daily Pivot: $${PP.toFixed(2)}`);
          } else {
            log(`⚠️ Insufficient candle data for ${symbol}`);
          }

        } catch (error) {
          log(`❌ Failed to get OHLC data for ${symbol}: ${error.message}`);
        }
      }

    } catch (error) {
      log(`❌ Failed to calculate OHLC levels: ${error.message}`);
    }
  }

  /**
   * Analyze symbol for trading opportunity
   */
  async analyzeSymbolForTrade(symbol) {
    try {
      // Skip if already have position
      if (this.activePositions.has(symbol)) {
        return;
      }
      
      // Get current price (simulate market data)
      const currentPrice = await this.getCurrentPrice(symbol);
      
      // Get daily levels
      const levels = this.dailyLevels.get(symbol);
      if (!levels) return;
      
      // Perform OHLC zone analysis
      const zoneAnalysis = this.performOHLCZoneAnalysis(currentPrice, levels);
      
      if (zoneAnalysis.inZone && zoneAnalysis.strength >= this.config.minZoneStrength) {
        
        // Calculate confluence score
        const confluenceScore = this.calculateConfluenceScore(zoneAnalysis);
        
        log(`🎯 ${symbol} Zone Analysis:`);
        log(`   Zone: ${zoneAnalysis.zoneName} (${zoneAnalysis.strength}% strength)`);
        log(`   Confluence: ${(confluenceScore * 100).toFixed(0)}%`);
        
        if (confluenceScore >= this.config.confluenceThreshold &&
            zoneAnalysis.signal.action !== 'wait') {

          // Execute trade on Delta Exchange testnet
          const trade = await this.executeLiveTrade(symbol, currentPrice, zoneAnalysis, confluenceScore);

          if (trade) {
            log(`✅ LIVE TRADE EXECUTED ON DELTA EXCHANGE TESTNET:`);
            log(`   ${symbol} ${trade.side} @ $${currentPrice.toFixed(2)}`);
            log(`   Risk: ${this.config.riskPerTrade}% | R:R = 1:${this.config.takeProfitRatio}`);
            log(`   Trade ID: ${trade.tradeId}`);
            this.activePositions.set(trade.tradeId, trade);
          }
        } else {
          log(`⏸️ ${symbol} confluence too low: ${(confluenceScore * 100).toFixed(0)}% < ${(this.config.confluenceThreshold * 100).toFixed(0)}%`);
        }
      }
      
    } catch (error) {
      log(`❌ Failed to analyze ${symbol}: ${error.message}`);
    }
  }

  /**
   * Get current market price from YOUR Delta Exchange
   */
  async getCurrentPrice(symbol) {
    try {
      // Get real market data from your Delta Exchange
      const marketData = await this.deltaService.getMarketData(symbol);

      // Use mark price (more stable) or last price
      const currentPrice = parseFloat(marketData.mark_price || marketData.last_price);

      log(`💹 ${symbol} Current Price: $${currentPrice.toFixed(2)}`);

      return currentPrice;

    } catch (error) {
      log(`❌ Failed to get real price for ${symbol}: ${error.message}`);

      // Fallback to approximate prices if API fails
      const fallbackPrice = symbol === 'BTCUSD' ? 105000 : 2650;
      log(`⚠️ Using fallback price for ${symbol}: $${fallbackPrice}`);
      return fallbackPrice;
    }
  }

  /**
   * Perform OHLC zone analysis
   */
  performOHLCZoneAnalysis(currentPrice, levels) {
    const { PDH, PDL, PDC, PP } = levels;
    const zoneBuffer = this.config.zoneBuffer / 100;
    
    // Check each zone for interaction
    const zones = [
      { name: 'PDH_Resistance', level: PDH, type: 'resistance', strength: 90 },
      { name: 'PDL_Support', level: PDL, type: 'support', strength: 90 },
      { name: 'PDC_Pivot', level: PDC, type: 'pivot', strength: 80 },
      { name: 'Daily_Pivot', level: PP, type: 'pivot', strength: 75 }
    ];
    
    for (const zone of zones) {
      const distance = Math.abs(currentPrice - zone.level) / zone.level;
      
      if (distance <= zoneBuffer) {
        return {
          inZone: true,
          zoneName: zone.name,
          zoneType: zone.type,
          zoneLevel: zone.level,
          strength: zone.strength,
          distance: distance * 100,
          signal: this.generateZoneSignal(currentPrice, zone)
        };
      }
    }
    
    return { inZone: false };
  }

  /**
   * Generate trading signal from zone
   */
  generateZoneSignal(currentPrice, zone) {
    const stopLossPercent = this.config.stopLossPercent / 100;
    const takeProfitRatio = this.config.takeProfitRatio;
    
    let signal = 'wait';
    let stopLoss = 0;
    let takeProfit = 0;
    
    if (zone.type === 'resistance') {
      signal = 'sell';
      stopLoss = zone.level * (1 + stopLossPercent);
      takeProfit = currentPrice * (1 - (stopLossPercent * takeProfitRatio));
    } else if (zone.type === 'support') {
      signal = 'buy';
      stopLoss = zone.level * (1 - stopLossPercent);
      takeProfit = currentPrice * (1 + (stopLossPercent * takeProfitRatio));
    }
    
    return {
      action: signal,
      stopLoss: stopLoss,
      takeProfit: takeProfit,
      confidence: zone.strength / 100
    };
  }

  /**
   * Calculate confluence score
   */
  calculateConfluenceScore(zoneAnalysis) {
    let score = 0.4; // Base score
    
    // Zone strength (60% weight)
    score += (zoneAnalysis.strength / 100) * 0.6;
    
    // Distance bonus (closer = better)
    const distanceBonus = Math.max(0, (0.15 - zoneAnalysis.distance) / 0.15) * 0.2;
    score += distanceBonus;
    
    // Zone type bonus
    if (zoneAnalysis.zoneName.includes('PDH') || zoneAnalysis.zoneName.includes('PDL')) {
      score += 0.1; // 10% bonus for PDH/PDL
    }
    
    return Math.min(0.95, score);
  }

  /**
   * Execute REAL trade on YOUR Delta Exchange India account
   */
  async executeLiveTrade(symbol, currentPrice, zoneAnalysis, confluenceScore) {
    try {
      // ENHANCED POSITION SIZING WITH PROPER LEVERAGE UTILIZATION
      const riskAmount = (this.config.riskPerTrade / 100) * this.balance.availableBalance;
      const stopLossPrice = zoneAnalysis.signal.stopLoss;
      const stopDistance = Math.abs(currentPrice - stopLossPrice) / currentPrice;

      // Delta Exchange India leverage settings
      const leverage = symbol === 'BTCUSD' ? 50 : 25; // Conservative leverage: BTC: 50x, ETH: 25x

      // Delta Exchange contract specifications (from API verification)
      const contractSize = symbol === 'BTCUSD' ? 0.001 : 0.01; // BTC or ETH per contract
      const contractValueUSD = contractSize * currentPrice; // USD value per contract

      // IMPROVED POSITION SIZING: Use percentage of balance for position value
      const positionSizePercent = 15; // Use 15% of balance for each position
      const maxPositionValue = (positionSizePercent / 100) * this.balance.availableBalance * leverage;

      // Calculate optimal contracts based on position value
      const optimalContracts = Math.floor(maxPositionValue / contractValueUSD);

      // Risk-based contract calculation (backup method)
      const riskBasedContracts = Math.floor((riskAmount * leverage) / (stopDistance * contractValueUSD));

      // Use the smaller of optimal or risk-based (more conservative)
      const calculatedContracts = Math.min(optimalContracts, riskBasedContracts);

      // Delta Exchange minimum: 1 contract
      const minContracts = 1;
      // Set reasonable maximums based on account size and API limits
      const maxContracts = symbol === 'BTCUSD' ? 50 : 200; // Conservative maximums for testnet

      const finalContracts = Math.max(minContracts, Math.min(calculatedContracts, maxContracts));
      const finalPositionValueUSD = finalContracts * contractValueUSD;
      const actualRiskUSD = finalPositionValueUSD / leverage;

      log(`💡 ENHANCED POSITION SIZING CALCULATION (OPTIMIZED FOR DELTA EXCHANGE):`);
      log(`   Account Balance: $${this.balance.availableBalance.toFixed(2)}`);
      log(`   Position Size (${positionSizePercent}% of balance): $${(positionSizePercent/100 * this.balance.availableBalance).toFixed(2)}`);
      log(`   Risk Amount (${this.config.riskPerTrade}%): $${riskAmount.toFixed(2)}`);
      log(`   Leverage: ${leverage}x (conservative)`);
      log(`   Max Position Value: $${maxPositionValue.toFixed(2)}`);
      log(`   Contract Size: ${contractSize} ${symbol.replace('USD', '')} per contract`);
      log(`   Contract Value: $${contractValueUSD.toFixed(2)} per contract`);
      log(`   Optimal Contracts: ${optimalContracts}`);
      log(`   Risk-Based Contracts: ${riskBasedContracts}`);
      log(`   Final Contracts: ${finalContracts} (minimum: ${minContracts})`);
      log(`   Position Value: $${finalPositionValueUSD.toFixed(2)}`);
      log(`   Actual Risk: $${actualRiskUSD.toFixed(2)}`);
      log(`   Stop Distance: ${(stopDistance * 100).toFixed(2)}%`);

      if (finalContracts < 1) {
        log(`⚠️ Cannot place trade: Need at least 1 contract (${contractSize} ${symbol.replace('USD', '')})`);
        log(`💡 Increase account balance or risk percentage`);
        return null;
      }

      // Get product ID for Delta Exchange India TESTNET
      const productIds = { 'BTCUSD': 84, 'ETHUSD': 1699 };
      const productId = productIds[symbol];

      if (!productId) {
        log(`❌ Product ID not found for ${symbol}`);
        return null;
      }

      // Create trade record
      const trade = {
        tradeId: `INDIA_${Date.now()}`,
        symbol: symbol,
        side: zoneAnalysis.signal.action.toUpperCase(),
        entryPrice: currentPrice,
        positionSize: finalContracts,
        positionValueUSD: finalPositionValueUSD,
        contractSize: contractSize,
        stopLoss: zoneAnalysis.signal.stopLoss,
        takeProfit: zoneAnalysis.signal.takeProfit,
        confluenceScore: confluenceScore,
        entryTime: Date.now(),
        status: 'ACTIVE',
        productId: productId,
        riskAmount: actualRiskUSD,
        leverage: leverage,
        currency: this.balance.currency
      };

      // PLACE REAL ORDER ON YOUR DELTA EXCHANGE INDIA ACCOUNT
      log(`📤 Placing REAL ${trade.side} order on YOUR Delta Exchange India account...`);
      log(`   Symbol: ${symbol} | Contracts: ${finalContracts} (${(finalContracts * contractSize).toFixed(6)} ${symbol.replace('USD', '')})`);
      log(`   Entry Price: $${currentPrice.toFixed(2)} | Leverage: ${leverage}x`);
      log(`   Risk: $${actualRiskUSD.toFixed(2)} | Position Value: $${finalPositionValueUSD.toFixed(2)}`);
      log(`   Stop Loss: $${trade.stopLoss.toFixed(2)} | Take Profit: $${trade.takeProfit.toFixed(2)}`);

      // Map signal action to valid Delta Exchange side
      let side;
      if (trade.side === 'SELL') {
        side = 'sell';
      } else if (trade.side === 'BUY') {
        side = 'buy';
      } else {
        log(`⚠️ Invalid signal action: ${trade.side}, skipping trade`);
        return null;
      }

      const orderParams = {
        product_id: productId,
        side: side,
        size: finalContracts,  // Use integer contract count, not decimal
        order_type: 'market_order',
        time_in_force: 'ioc'  // Immediate or Cancel for market orders
      };

      log(`🔄 Executing order with params:`, JSON.stringify(orderParams, null, 2));

      try {
        // Execute the real order
        const order = await this.deltaService.placeOrder(orderParams);

        log(`✅ REAL ORDER PLACED SUCCESSFULLY ON DELTA EXCHANGE INDIA!`);
        log(`   Order ID: ${order.id}`);
        log(`   Status: ${order.state}`);
        log(`   Product: ${symbol} (ID: ${productId})`);

        // Store order details
        trade.orderId = order.id;
        trade.orderState = order.state;

      } catch (orderError) {
        log(`❌ Failed to place real order: ${orderError.message}`);
        log(`⚠️ This was a real trading attempt on your Delta Exchange India account`);

        // Log detailed error for debugging
        console.log('RAW ERROR:', orderError);
        console.log('ERROR RESPONSE:', orderError.response);
        console.log('ERROR DATA:', orderError.response?.data);
        console.log('ORDER PARAMS:', orderParams);

        if (orderError.response?.data) {
          log(`🔍 Delta Exchange API Error:`, JSON.stringify(orderError.response.data, null, 2));
        }

        // Log the exact request that failed
        log(`🔍 Failed request params:`, JSON.stringify(orderParams, null, 2));

        return null;
      }

      // Add to active positions
      this.activePositions.set(trade.tradeId, trade);
      this.performance.totalTrades++;

      log(`🎉 REAL TRADE EXECUTED ON YOUR DELTA EXCHANGE INDIA ACCOUNT!`);
      log(`   Trade ID: ${trade.tradeId} | Order ID: ${trade.orderId}`);
      log(`   This trade will reflect in your Delta Exchange India account balance!`);

      return trade;

    } catch (error) {
      log(`❌ Failed to execute real trade: ${error.message}`);
      return null;
    }
  }

  /**
   * Manage active positions
   */
  async manageActivePositions() {
    if (this.activePositions.size === 0) return;
    
    log(`🎛️ Managing ${this.activePositions.size} active position(s)...`);
    
    const positionsToClose = [];
    
    for (const [tradeId, trade] of this.activePositions) {
      try {
        const currentPrice = await this.getCurrentPrice(trade.symbol);
        
        // Check stop loss and take profit
        let shouldClose = false;
        let closeReason = '';
        
        if ((trade.side === 'BUY' && currentPrice <= trade.stopLoss) ||
            (trade.side === 'SELL' && currentPrice >= trade.stopLoss)) {
          shouldClose = true;
          closeReason = 'STOP_LOSS';
        } else if ((trade.side === 'BUY' && currentPrice >= trade.takeProfit) ||
                   (trade.side === 'SELL' && currentPrice <= trade.takeProfit)) {
          shouldClose = true;
          closeReason = 'TAKE_PROFIT';
        }
        
        if (shouldClose) {
          await this.closeLiveTrade(trade, currentPrice, closeReason);
          positionsToClose.push(tradeId);
        }
        
      } catch (error) {
        log(`❌ Failed to manage position ${tradeId}: ${error.message}`);
      }
    }
    
    // Remove closed positions
    positionsToClose.forEach(tradeId => {
      this.activePositions.delete(tradeId);
    });
  }

  /**
   * Close live trade on Delta Exchange testnet
   */
  async closeLiveTrade(trade, closePrice, closeReason) {
    try {
      // Calculate P&L
      let pnlPercent;
      if (trade.side === 'BUY') {
        pnlPercent = (closePrice - trade.entryPrice) / trade.entryPrice;
      } else {
        pnlPercent = (trade.entryPrice - closePrice) / trade.entryPrice;
      }
      
      const pnlDollars = pnlPercent * (this.config.riskPerTrade / 100) * this.balance.availableBalance;
      const profitable = pnlDollars > 0;
      
      // In production, this would close the position on Delta Exchange:
      // await this.deltaService.closePosition(trade.positionId);
      
      log(`📤 Closing ${trade.symbol} position on Delta Exchange testnet...`);
      await this.sleep(1000); // Simulate order execution delay
      
      // Update performance
      this.performance.totalPnL += pnlDollars;
      if (profitable) {
        this.performance.winningTrades++;
      }
      this.performance.winRate = (this.performance.winningTrades / this.performance.totalTrades) * 100;
      
      // Log trade result
      const profitEmoji = profitable ? '✅' : '❌';
      const pnlSign = pnlPercent >= 0 ? '+' : '';
      
      log(`${profitEmoji} TRADE CLOSED ON DELTA EXCHANGE TESTNET:`);
      log(`   ${trade.symbol} ${trade.side} - ${closeReason}`);
      log(`   P&L: ${pnlSign}${(pnlPercent * 100).toFixed(2)}% ($${pnlSign}${pnlDollars.toFixed(2)})`);
      log(`   Trade ID: ${trade.tradeId}`);
      
    } catch (error) {
      log(`❌ Failed to close live trade: ${error.message}`);
    }
  }

  /**
   * Display current trading status
   */
  displayTradingStatus() {
    log('\n📊 LIVE TRADING STATUS');
    log('─'.repeat(60));
    log(`💰 Total P&L: $${this.performance.totalPnL.toFixed(2)}`);
    log(`📈 Win Rate: ${this.performance.winRate.toFixed(1)}% (${this.performance.winningTrades}/${this.performance.totalTrades})`);
    log(`🎛️ Active Positions: ${this.activePositions.size}/${this.config.maxPositions}`);
    
    if (this.activePositions.size > 0) {
      log('📋 Active Trades:');
      for (const [tradeId, trade] of this.activePositions) {
        log(`   ${trade.symbol} ${trade.side} @ $${trade.entryPrice.toFixed(2)} (${trade.tradeId})`);
      }
    }
    log('─'.repeat(60));
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  const liveTrading = new DeltaTestnetLiveTrading();
  
  try {
    await liveTrading.startLiveTrading();
    
  } catch (error) {
    log(`❌ Failed to run live trading: ${error.message}`);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('🛑 Received SIGINT, shutting down live trading...');
  process.exit(0);
});

// Run the live trading system
if (require.main === module) {
  main().catch(error => {
    log(`❌ Unhandled error: ${error.message}`);
    process.exit(1);
  });
}
