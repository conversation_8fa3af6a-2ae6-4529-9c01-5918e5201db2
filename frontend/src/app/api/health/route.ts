import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Check if backend is reachable
    let backendStatus = 'unknown';
    let backendResponse = null;
    
    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005';
      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add timeout to prevent hanging
        signal: AbortSignal.timeout(5000),
      });
      
      if (response.ok) {
        backendStatus = 'healthy';
        backendResponse = await response.json();
      } else {
        backendStatus = 'unhealthy';
      }
    } catch (error) {
      backendStatus = 'unreachable';
      console.warn('Backend health check failed:', error);
    }

    // Check ML system
    let mlStatus = 'unknown';
    let mlResponse = null;
    
    try {
      const mlUrl = process.env.NEXT_PUBLIC_ML_URL || 'http://localhost:8000';
      const response = await fetch(`${mlUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000),
      });
      
      if (response.ok) {
        mlStatus = 'healthy';
        mlResponse = await response.json();
      } else {
        mlStatus = 'unhealthy';
      }
    } catch (error) {
      mlStatus = 'unreachable';
      console.warn('ML system health check failed:', error);
    }

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'frontend',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      dependencies: {
        backend: {
          status: backendStatus,
          url: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005',
          response: backendResponse
        },
        mlSystem: {
          status: mlStatus,
          url: process.env.NEXT_PUBLIC_ML_URL || 'http://localhost:8000',
          response: mlResponse
        }
      },
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
        }
      }
    };

    // Determine overall health status
    if (backendStatus === 'unreachable' || mlStatus === 'unreachable') {
      healthStatus.status = 'degraded';
    } else if (backendStatus === 'unhealthy' || mlStatus === 'unhealthy') {
      healthStatus.status = 'unhealthy';
    }

    const statusCode = healthStatus.status === 'healthy' ? 200 : 
                      healthStatus.status === 'degraded' ? 200 : 503;

    return NextResponse.json(healthStatus, { status: statusCode });

  } catch (error) {
    console.error('Frontend health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'frontend',
      error: error instanceof Error ? error.message : 'Unknown error',
      uptime: process.uptime()
    }, { status: 503 });
  }
}

// Handle CORS preflight
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
