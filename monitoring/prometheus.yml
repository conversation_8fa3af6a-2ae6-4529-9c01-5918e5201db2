# Prometheus Configuration for SmartMarketOOPS
# Monitors all system components with trading-specific metrics

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'smartmarket-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "rules/*.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # ML Trading System
  - job_name: 'ml-system'
    static_configs:
      - targets: ['ml-system:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    params:
      format: ['prometheus']

  # Analysis-Execution Bridge
  - job_name: 'bridge'
    static_configs:
      - targets: ['bridge:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s  # More frequent for real-time trading
    scrape_timeout: 3s

  # Frontend Application
  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  # Database Monitoring
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s

  - job_name: 'questdb'
    static_configs:
      - targets: ['questdb:9000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # System Monitoring
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Trading-Specific Monitoring
  - job_name: 'trading-metrics'
    static_configs:
      - targets: ['ml-system:8000', 'bridge:8000']
    metrics_path: '/trading/metrics'
    scrape_interval: 5s  # High frequency for trading metrics
    params:
      module: ['trading']

  # Delta Exchange API Monitoring
  - job_name: 'delta-exchange-health'
    static_configs:
      - targets: ['bridge:8000']
    metrics_path: '/external/delta/health'
    scrape_interval: 30s

  # WebSocket Connection Monitoring
  - job_name: 'websocket-metrics'
    static_configs:
      - targets: ['bridge:8000']
    metrics_path: '/ws/metrics'
    scrape_interval: 10s

  # ML Model Performance
  - job_name: 'ml-model-metrics'
    static_configs:
      - targets: ['ml-system:8000']
    metrics_path: '/ml/metrics'
    scrape_interval: 60s  # Model metrics don't change as frequently

  # Risk Management Metrics
  - job_name: 'risk-metrics'
    static_configs:
      - targets: ['bridge:8000']
    metrics_path: '/risk/metrics'
    scrape_interval: 10s

# Remote write configuration for long-term storage
remote_write:
  - url: "https://prometheus-remote-write.smartmarket.com/api/v1/write"
    basic_auth:
      username: "smartmarket"
      password_file: "/etc/prometheus/remote_write_password"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Recording rules for complex calculations
recording_rules:
  - name: trading_performance
    interval: 30s
    rules:
      - record: trading:win_rate_5m
        expr: |
          (
            sum(rate(successful_trades_total[5m])) /
            sum(rate(total_trades_total[5m]))
          ) * 100

      - record: trading:pnl_rate_1h
        expr: |
          sum(rate(portfolio_pnl_total[1h]))

      - record: trading:sharpe_ratio_daily
        expr: |
          (
            avg_over_time(daily_return[24h]) /
            stddev_over_time(daily_return[24h])
          ) * sqrt(365)

  - name: system_performance
    interval: 15s
    rules:
      - record: system:api_latency_p95
        expr: |
          histogram_quantile(0.95, 
            sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service)
          )

      - record: system:error_rate_5m
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) /
            sum(rate(http_requests_total[5m]))
          ) * 100

      - record: system:throughput_rps
        expr: |
          sum(rate(http_requests_total[1m])) by (service)

# Alerting rules
alerting_rules:
  - name: trading_alerts
    rules:
      - alert: HighDrawdown
        expr: current_drawdown_percent > 15
        for: 1m
        labels:
          severity: critical
          component: trading
        annotations:
          summary: "High drawdown detected"
          description: "Current drawdown is {{ $value }}%, exceeding 15% threshold"

      - alert: LowWinRate
        expr: trading:win_rate_5m < 50
        for: 5m
        labels:
          severity: warning
          component: trading
        annotations:
          summary: "Win rate below threshold"
          description: "5-minute win rate is {{ $value }}%, below 50% threshold"

      - alert: TradingSystemDown
        expr: up{job="ml-system"} == 0 or up{job="bridge"} == 0
        for: 30s
        labels:
          severity: critical
          component: system
        annotations:
          summary: "Trading system component down"
          description: "{{ $labels.job }} is down"

  - name: system_alerts
    rules:
      - alert: HighAPILatency
        expr: system:api_latency_p95 > 1
        for: 2m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High API latency"
          description: "95th percentile latency is {{ $value }}s"

      - alert: HighErrorRate
        expr: system:error_rate_5m > 5
        for: 1m
        labels:
          severity: critical
          component: api
        annotations:
          summary: "High error rate"
          description: "Error rate is {{ $value }}% over 5 minutes"

      - alert: DatabaseConnectionIssues
        expr: up{job="postgres"} == 0 or up{job="redis"} == 0
        for: 30s
        labels:
          severity: critical
          component: database
        annotations:
          summary: "Database connection issues"
          description: "{{ $labels.job }} database is unreachable"
