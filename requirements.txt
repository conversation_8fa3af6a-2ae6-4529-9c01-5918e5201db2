# SmartMarketOOPS - Consolidated Python Requirements
# Unified requirements file combining all ML, trading, and system dependencies
# Compatible with M2 MacBook Air 8GB RAM and production environments

# ============================================================================
# CORE TRADING & ML DEPENDENCIES
# ============================================================================

# Trading Exchange Integration
ccxt>=4.4.88                    # Cryptocurrency exchange integration

# Core Data Science Stack
pandas>=1.5.0                   # Data manipulation and analysis
numpy>=1.24.0                   # Numerical computing (updated from ml/)
scikit-learn>=1.2.0             # Machine learning algorithms

# Deep Learning Framework
torch>=2.0.0                    # PyTorch for neural networks
scipy>=1.10.0                   # Scientific computing
joblib>=1.2.0                   # Parallel processing

# ============================================================================
# TECHNICAL ANALYSIS & VISUALIZATION
# ============================================================================

# Technical Analysis
ta>=0.10.2                      # Technical analysis indicators
mplfinance>=0.12.9b7            # Financial plotting
# talib-binary>=0.4.24          # Optional - install manually if needed

# Data Visualization
matplotlib>=3.7.0               # Plotting library (updated from ml/)
plotly>=5.15.0                  # Interactive plots
seaborn>=0.12.0                 # Statistical visualization
scikit-image>=0.20.0            # Image processing

# ============================================================================
# WEB FRAMEWORK & API
# ============================================================================

# FastAPI Stack
fastapi>=0.103.0                # Web framework (updated from ml/)
uvicorn[standard]>=0.23.0       # ASGI server (updated from ml/)
pydantic>=2.0.0                 # Data validation (updated from ml/)

# HTTP & WebSocket
websockets>=11.0.0              # WebSocket support
aiohttp>=3.8.0                  # Async HTTP client
requests>=2.31.0                # HTTP library (updated from ml/)
httpx>=0.24.0                   # Modern HTTP client

# ============================================================================
# ML OPTIMIZATION & HYPERPARAMETER TUNING
# ============================================================================

# Optimization Libraries
optuna>=3.2.0                   # Hyperparameter optimization (updated from ml/)
hyperopt>=0.2.7                 # Bayesian optimization
bayesian-optimization>=1.4.0    # Gaussian process optimization

# ML Utilities
tensorboard>=2.12.0             # ML experiment tracking
typing-extensions>=4.5.0        # Type hints support
tqdm>=4.65.0                    # Progress bars
python-multipart>=0.0.6         # File upload support

# ============================================================================
# DATABASE & CACHING
# ============================================================================

# Database Drivers
redis>=4.5.0                    # Redis cache
psycopg2-binary>=2.9.0          # PostgreSQL driver
sqlalchemy>=2.0.0               # ORM
alembic>=1.11.0                 # Database migrations

# Async Database
asyncpg>=0.28.0                 # Async PostgreSQL driver
aiofiles>=23.0.0                # Async file operations

# ============================================================================
# CONFIGURATION & ENVIRONMENT
# ============================================================================

# Configuration Management
python-dotenv>=1.0.0            # Environment variables
pyyaml>=6.0.0                   # YAML configuration

# ============================================================================
# MONITORING & LOGGING
# ============================================================================

# Monitoring & Metrics
prometheus-client>=0.17.0       # Metrics collection
structlog>=23.0.0               # Structured logging
loguru>=0.7.0                   # Advanced logging

# ============================================================================
# SECURITY & AUTHENTICATION
# ============================================================================

# Security Libraries
cryptography>=41.0.0            # Cryptographic functions
passlib>=1.7.4                  # Password hashing
python-jose>=3.3.0              # JWT handling

# ============================================================================
# DEVELOPMENT & TESTING
# ============================================================================

# Testing Framework
pytest>=7.3.0                   # Testing framework
pytest-cov>=4.1.0               # Coverage reporting

# Code Quality & Linting
ruff>=0.1.0                     # Fast Python linter
black>=23.10.0                  # Code formatter
isort>=5.12.0                   # Import sorter
mypy>=1.6.0                     # Type checker
bandit>=1.7.5                   # Security linter

# ============================================================================
# OPTIONAL EXTRAS (Install as needed)
# ============================================================================

# [ml-minimal] - For M2 MacBook Air 8GB RAM (lightweight ML)
# Install with: pip install -r requirements.txt --extra-index-url https://download.pytorch.org/whl/cpu

# [production] - For production deployment
# Includes all monitoring, security, and performance packages

# [development] - For development environment
# Includes all testing, linting, and debugging tools