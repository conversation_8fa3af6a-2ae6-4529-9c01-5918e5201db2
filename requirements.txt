# Core ML and Trading Dependencies
ccxt>=4.0.0
pandas>=1.5.0
numpy>=1.23.0
scikit-learn>=1.2.0
torch>=2.0.0
scipy>=1.10.0
joblib>=1.2.0

# Technical Analysis
ta>=0.10.2
mplfinance>=0.12.9b7
# talib-binary>=0.4.24  # Optional - install manually if needed

# Web Framework and API
fastapi>=0.95.0
uvicorn[standard]>=0.22.0
websockets>=11.0.0
aiohttp>=3.8.0
requests>=2.28.0
pydantic>=1.10.0

# Data Processing and Visualization
matplotlib>=3.5.0
plotly>=5.15.0
seaborn>=0.12.0
scikit-image>=0.20.0

# ML Optimization and Hyperparameter Tuning
optuna>=3.0.0
hyperopt>=0.2.7
bayesian-optimization>=1.4.0

# Database and Caching
redis>=4.5.0
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
alembic>=1.11.0

# Configuration and Environment
python-dotenv>=1.0.0
pyyaml>=6.0.0
# configparser>=5.3.0  # Built-in module

# Async and Concurrency
# asyncio>=3.4.3  # Built-in module
aiofiles>=23.0.0
asyncpg>=0.28.0

# Monitoring and Logging
prometheus-client>=0.17.0
structlog>=23.0.0
loguru>=0.7.0

# Security and Authentication
cryptography>=41.0.0
passlib>=1.7.4
python-jose>=3.3.0

# Linting and QC tools
ruff>=0.1.0
black>=23.10.0
isort>=5.12.0
mypy>=1.6.0
bandit>=1.7.5
pytest>=7.3.0
pytest-cov>=4.1.0 