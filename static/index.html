<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartMarketOOPS - Optimized Trading System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #a8d8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1.1rem;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #a8d8ff;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-weight: bold;
        }
        
        .positive {
            color: #4ade80;
        }
        
        .negative {
            color: #f87171;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy {
            background-color: #4ade80;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #4ade80, #22c55e);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 222, 128, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(99, 102, 241, 0.4);
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SmartMarketOOPS</h1>
            <p>Memory-Optimized ML Trading System for M2 MacBook Air</p>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3><span class="status-indicator status-healthy"></span>System Status</h3>
                <div class="metric">
                    <span>Status:</span>
                    <span class="metric-value positive" id="system-status">Running</span>
                </div>
                <div class="metric">
                    <span>Data Status:</span>
                    <span class="metric-value" id="data-status">Loading...</span>
                </div>
                <div class="metric">
                    <span>Mode:</span>
                    <span class="metric-value">Optimized</span>
                </div>
                <div class="metric">
                    <span>Memory:</span>
                    <span class="metric-value positive">Low Usage</span>
                </div>
                <div class="metric">
                    <span>Uptime:</span>
                    <span class="metric-value" id="uptime">00:00:00</span>
                </div>
            </div>
            
            <div class="card">
                <h3>Portfolio Overview</h3>
                <div class="metric">
                    <span>Total Value:</span>
                    <span class="metric-value" id="total-value">$10,000.00</span>
                </div>
                <div class="metric">
                    <span>Daily P&L:</span>
                    <span class="metric-value positive" id="daily-pnl">+$100.00 (1.0%)</span>
                </div>
                <div class="metric">
                    <span>Win Rate:</span>
                    <span class="metric-value positive" id="win-rate">65.0%</span>
                </div>
                <div class="metric">
                    <span>Active Positions:</span>
                    <span class="metric-value" id="positions">2</span>
                </div>
            </div>
            
            <div class="card">
                <h3>Trading Performance</h3>
                <div class="metric">
                    <span>Total Trades:</span>
                    <span class="metric-value" id="total-trades">0</span>
                </div>
                <div class="metric">
                    <span>Sharpe Ratio:</span>
                    <span class="metric-value positive">1.5</span>
                </div>
                <div class="metric">
                    <span>Max Drawdown:</span>
                    <span class="metric-value negative">-5.0%</span>
                </div>
                <div class="metric">
                    <span>Model Accuracy:</span>
                    <span class="metric-value positive">75.0%</span>
                </div>
            </div>
            
            <div class="card">
                <h3>Real-Time Signals</h3>
                <div id="signals-container">
                    <div class="metric">
                        <span>Loading...</span>
                        <span class="metric-value">Real-time data</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>Live Market Data</h3>
                <div id="market-data-container">
                    <div class="metric">
                        <span>Loading...</span>
                        <span class="metric-value">Real-time prices</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="actions">
            <a href="/docs" class="btn" target="_blank">API Documentation</a>
            <a href="/api/market-data" class="btn btn-secondary" target="_blank">Live Market Data</a>
            <a href="/api/signals" class="btn btn-secondary" target="_blank">Trading Signals</a>
            <button class="btn" onclick="refreshData()" id="refresh-btn">🔄 Refresh Now</button>
            <button class="btn btn-secondary" onclick="toggleAutoRefresh()" id="auto-refresh-btn">⏸️ Pause Auto-Refresh</button>
        </div>
        
        <div class="footer">
            <p>SmartMarketOOPS v2.0.0-optimized | Designed for M2 MacBook Air 8GB RAM</p>
        </div>
    </div>
    
    <script>
        let startTime = Date.now();
        let autoRefreshInterval = null;
        let autoRefreshEnabled = true;

        function updateUptime() {
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            document.getElementById('uptime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        async function refreshData() {
            try {
                console.log('🔄 Refreshing data...');

                // Update refresh button to show loading
                const refreshBtn = document.getElementById('refresh-btn');
                if (refreshBtn) {
                    refreshBtn.textContent = '⏳ Refreshing...';
                    refreshBtn.disabled = true;
                }

                // Update data status to show loading
                const dataStatusEl = document.getElementById('data-status');
                if (dataStatusEl) {
                    dataStatusEl.textContent = 'Updating...';
                    dataStatusEl.className = 'metric-value';
                }

                // Fetch portfolio data
                const portfolioResponse = await fetch('/api/portfolio');
                if (!portfolioResponse.ok) {
                    throw new Error(`Portfolio API error: ${portfolioResponse.status}`);
                }
                const portfolio = await portfolioResponse.json();
                console.log('📊 Portfolio data:', portfolio);

                // Update portfolio display with error checking
                const totalValueEl = document.getElementById('total-value');
                if (totalValueEl) {
                    totalValueEl.textContent = `$${(portfolio.totalValue || 0).toLocaleString()}`;
                }

                const dailyPnlElement = document.getElementById('daily-pnl');
                if (dailyPnlElement) {
                    const pnlClass = (portfolio.dailyPnL || 0) >= 0 ? 'positive' : 'negative';
                    dailyPnlElement.className = `metric-value ${pnlClass}`;
                    dailyPnlElement.textContent =
                        `${(portfolio.dailyPnL || 0) >= 0 ? '+' : ''}$${(portfolio.dailyPnL || 0).toFixed(2)} (${(portfolio.dailyPnLPercentage || 0).toFixed(1)}%)`;
                }

                const winRateEl = document.getElementById('win-rate');
                if (winRateEl) {
                    winRateEl.textContent = `${portfolio.winRate || 0}%`;
                }

                const positionsEl = document.getElementById('positions');
                if (positionsEl) {
                    positionsEl.textContent = portfolio.positions || 0;
                }

                // Fetch performance data
                const performanceResponse = await fetch('/api/performance');
                if (!performanceResponse.ok) {
                    throw new Error(`Performance API error: ${performanceResponse.status}`);
                }
                const performance = await performanceResponse.json();
                console.log('📈 Performance data:', performance);

                const totalTradesEl = document.getElementById('total-trades');
                if (totalTradesEl) {
                    totalTradesEl.textContent = performance.totalTrades || 0;
                }

                // Fetch real-time signals
                const signalsResponse = await fetch('/api/signals');
                if (!signalsResponse.ok) {
                    throw new Error(`Signals API error: ${signalsResponse.status}`);
                }
                const signals = await signalsResponse.json();
                console.log('🎯 Signals data:', signals);

                const signalsContainer = document.getElementById('signals-container');
                if (signalsContainer) {
                    signalsContainer.innerHTML = '';

                    if (signals && signals.length > 0) {
                        signals.slice(0, 3).forEach(signal => {
                            const actionClass = signal.action === 'buy' ? 'positive' : signal.action === 'sell' ? 'negative' : '';
                            const confidenceDisplay = signal.confidence ? `${signal.confidence}%` : 'N/A';
                            signalsContainer.innerHTML += `
                                <div class="metric">
                                    <span>${signal.symbol || 'Unknown'}:</span>
                                    <span class="metric-value ${actionClass}">${(signal.action || 'hold').toUpperCase()} (${confidenceDisplay})</span>
                                </div>
                            `;
                        });
                    } else {
                        signalsContainer.innerHTML = '<div class="metric"><span>No signals</span><span class="metric-value">Analyzing...</span></div>';
                    }
                }

                // Fetch real-time market data
                const marketResponse = await fetch('/api/market-data');
                if (!marketResponse.ok) {
                    throw new Error(`Market data API error: ${marketResponse.status}`);
                }
                const marketData = await marketResponse.json();
                console.log('💹 Market data:', marketData);

                const marketContainer = document.getElementById('market-data-container');
                if (marketContainer) {
                    marketContainer.innerHTML = '';

                    if (marketData && marketData.data && marketData.data.length > 0) {
                        // Group by symbol to avoid duplicates
                        const uniqueData = {};
                        marketData.data.forEach(data => {
                            if (!uniqueData[data.symbol] || data.timestamp > uniqueData[data.symbol].timestamp) {
                                uniqueData[data.symbol] = data;
                            }
                        });

                        Object.values(uniqueData).slice(0, 3).forEach(data => {
                            const price = data.price || 0;
                            marketContainer.innerHTML += `
                                <div class="metric">
                                    <span>${data.symbol || 'Unknown'}:</span>
                                    <span class="metric-value">$${price.toLocaleString()}</span>
                                </div>
                            `;
                        });
                    } else {
                        marketContainer.innerHTML = '<div class="metric"><span>Loading...</span><span class="metric-value">Market data</span></div>';
                    }
                }

                // Update data status
                const dataStatusEl = document.getElementById('data-status');
                if (dataStatusEl) {
                    dataStatusEl.textContent = 'Live';
                    dataStatusEl.className = 'metric-value positive';
                }

                // Reset refresh button
                const refreshBtn = document.getElementById('refresh-btn');
                if (refreshBtn) {
                    refreshBtn.textContent = '🔄 Refresh Now';
                    refreshBtn.disabled = false;
                }

                console.log('✅ Real-time data refreshed successfully');
            } catch (error) {
                console.error('❌ Error refreshing data:', error);

                // Update data status to show error
                const dataStatusEl = document.getElementById('data-status');
                if (dataStatusEl) {
                    dataStatusEl.textContent = 'Error';
                    dataStatusEl.className = 'metric-value negative';
                }

                // Reset refresh button
                const refreshBtn = document.getElementById('refresh-btn');
                if (refreshBtn) {
                    refreshBtn.textContent = '🔄 Refresh Now';
                    refreshBtn.disabled = false;
                }

                // Show error in UI
                const errorElements = ['signals-container', 'market-data-container'];
                errorElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.innerHTML = `<div class="metric"><span>Error</span><span class="metric-value negative">Failed to load</span></div>`;
                    }
                });
            }
        }

        function toggleAutoRefresh() {
            const btn = document.getElementById('auto-refresh-btn');

            if (autoRefreshEnabled) {
                // Disable auto-refresh
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                autoRefreshEnabled = false;
                btn.textContent = '▶️ Resume Auto-Refresh';
                btn.className = 'btn';
                console.log('🔴 Auto-refresh disabled');
            } else {
                // Enable auto-refresh
                autoRefreshInterval = setInterval(refreshData, 10000);
                autoRefreshEnabled = true;
                btn.textContent = '⏸️ Pause Auto-Refresh';
                btn.className = 'btn btn-secondary';
                console.log('🟢 Auto-refresh enabled');
            }
        }

        // Update uptime every second
        setInterval(updateUptime, 1000);

        // Start auto-refresh (10 seconds for real-time updates)
        autoRefreshInterval = setInterval(refreshData, 10000);

        // Initial data load
        refreshData();
    </script>
</body>
</html>
