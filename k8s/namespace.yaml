apiVersion: v1
kind: Namespace
metadata:
  name: smartmarket
  labels:
    name: smartmarket
    environment: production
    app: smartmarket-trading-system
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: smartmarket-quota
  namespace: smartmarket
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: smartmarket-limits
  namespace: smartmarket
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
