# Database
DATABASE_URL="postgresql://username:password@localhost:5432/dbname?schema=public"

# Encryption
# Generate with: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
ENCRYPTION_KEY=your_encryption_key_here_64_hex_characters
ENCRYPTION_KEY_SECONDARY=

# Prisma Accelerate
USE_PRISMA_ACCELERATE=false
# Only needed if USE_PRISMA_ACCELERATE is true
# PRISMA_ACCELERATE_API_KEY=your_accelerate_api_key_here 